#!/bin/bash

# 清理本地后端文件，保留容器所需的配置文件
# 此脚本将移除本地后端的代码文件，但保留Docker容器所需的配置文件

echo "开始清理本地后端文件..."

# 进入后端目录
cd /Users/<USER>/AIBUBB/backend

# 创建备份目录
BACKUP_DIR="../backend-backup-$(date +%Y%m%d%H%M%S)"
mkdir -p $BACKUP_DIR

# 备份重要文件
echo "备份重要文件到 $BACKUP_DIR..."
cp -r .env* $BACKUP_DIR/
cp -r Dockerfile $BACKUP_DIR/
cp -r docker-compose.yml $BACKUP_DIR/ 2>/dev/null
cp -r ecosystem.config.js $BACKUP_DIR/
cp -r package.json $BACKUP_DIR/
cp -r package-lock.json $BACKUP_DIR/

# 移除不需要的文件和目录，但保留Docker所需的文件
echo "移除不需要的文件和目录..."

# 保留的文件列表
KEEP_FILES=(
  ".env"
  ".env.docker"
  ".env.bak"
  ".dockerignore"
  "Dockerfile"
  "docker-compose.yml"
  "ecosystem.config.js"
  "package.json"
  "package-lock.json"
)

# 创建临时目录
TEMP_DIR="../backend-temp"
mkdir -p $TEMP_DIR

# 复制需要保留的文件到临时目录
for file in "${KEEP_FILES[@]}"; do
  if [ -f "$file" ]; then
    cp "$file" "$TEMP_DIR/"
  fi
done

# 移除所有文件和目录
rm -rf *

# 从临时目录恢复需要保留的文件
cp -r $TEMP_DIR/* ./

# 移除临时目录
rm -rf $TEMP_DIR

# 创建README文件，说明后端已移至容器
cat > README.md << 'EOF'
# AIBUBB 后端

本目录仅包含Docker容器所需的配置文件。所有后端代码和服务均在Docker容器中运行。

## 容器管理

### 启动容器
```bash
docker-compose up -d
```

### 查看容器状态
```bash
docker-compose ps
```

### 查看容器日志
```bash
docker-compose logs -f
```

### 停止容器
```bash
docker-compose down
```

## 容器信息

- **后端API**: aibubb-backend (端口: 9090)
- **MySQL数据库**: aibubb-mysql (端口: 3306)
- **Redis缓存**: aibubb-redis (端口: 6379)

## 注意事项

- 请勿在本地修改后端代码，所有修改应在容器内进行
- 如需更新后端代码，请重建容器
EOF

echo "清理完成！本地后端文件已移除，仅保留Docker容器所需的配置文件。"
echo "备份文件已保存到 $BACKUP_DIR"
