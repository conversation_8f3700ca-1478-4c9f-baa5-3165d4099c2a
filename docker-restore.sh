#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 检查参数
if [ $# -ne 1 ]; then
    echo -e "${RED}用法: $0 <备份文件路径>${NC}"
    echo -e "${YELLOW}示例: $0 ./backups/aibubb_backup_20230101_120000.sql.gz${NC}"
    exit 1
fi

BACKUP_FILE=$1

echo -e "${YELLOW}开始从 $BACKUP_FILE 恢复AIBUBB数据库...${NC}"

# 检查备份文件是否存在
if [ ! -f "$BACKUP_FILE" ]; then
    echo -e "${RED}错误: 备份文件不存在${NC}"
    exit 1
fi

# 检查MySQL容器是否运行
if ! docker ps | grep -q aibubb-mysql; then
    echo -e "${RED}错误: MySQL容器未运行${NC}"
    exit 1
fi

# 解压备份文件（如果是压缩文件）
if [[ "$BACKUP_FILE" == *.gz ]]; then
    echo -e "${YELLOW}解压备份文件...${NC}"
    UNCOMPRESSED_FILE="${BACKUP_FILE%.gz}"
    gunzip -c "$BACKUP_FILE" > "$UNCOMPRESSED_FILE"
    BACKUP_FILE="$UNCOMPRESSED_FILE"
fi

# 执行数据库恢复
echo -e "${YELLOW}正在恢复MySQL数据...${NC}"
cat "$BACKUP_FILE" | docker exec -i aibubb-mysql sh -c 'exec mysql -u aibubb_user -paibubb_password'

# 检查恢复是否成功
if [ $? -eq 0 ]; then
    echo -e "${GREEN}数据库恢复成功!${NC}"
    
    # 如果使用了临时解压文件，删除它
    if [[ "$BACKUP_FILE" != "$1" ]]; then
        rm "$BACKUP_FILE"
    fi
else
    echo -e "${RED}数据库恢复失败${NC}"
    
    # 如果使用了临时解压文件，删除它
    if [[ "$BACKUP_FILE" != "$1" ]]; then
        rm "$BACKUP_FILE"
    fi
    
    exit 1
fi
