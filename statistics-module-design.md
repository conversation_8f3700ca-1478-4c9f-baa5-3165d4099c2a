# 统计模块设计文档

## 1. 概述

本文档详细描述了AIBUBB项目统计模块的设计，包括仓库层和服务层的接口设计、数据模型、方法定义和优化策略。统计模块负责处理用户学习活动的记录、统计和分析，是评估用户学习效果和平台使用情况的重要组成部分。

## 2. 数据模型

统计模块主要涉及以下数据模型：

### 2.1 LearningActivity（学习活动）

```javascript
{
  id: Number,           // 主键
  user_id: String,      // 用户ID
  activity_type: String, // 活动类型
  content_id: Number,   // 内容ID（可选）
  content_type: String, // 内容类型（可选）
  plan_id: Number,      // 学习计划ID（可选）
  duration: Number,     // 持续时间（秒）
  metadata: JSON,       // 元数据（可选）
  created_at: Date,     // 创建时间
  updated_at: Date      // 更新时间
}
```

活动类型包括：
- `login`: 登录
- `view_exercise`: 查看练习
- `complete_exercise`: 完成练习
- `view_insight`: 查看观点
- `create_note`: 创建笔记
- `like_note`: 点赞笔记
- `comment_note`: 评论笔记
- `bubble_interaction`: 泡泡互动
- `share_content`: 分享内容

### 2.2 DailyRecord（每日记录）

```javascript
{
  id: Number,                // 主键
  user_id: String,           // 用户ID
  date: Date,                // 日期
  time_spent: Number,        // 学习时间（分钟）
  exercises_completed: Number, // 完成练习数
  insights_viewed: Number,   // 查看观点数
  notes_created: Number,     // 创建笔记数
  bubble_interactions: Number, // 泡泡互动数
  has_activity: Boolean,     // 是否有活动
  created_at: Date,          // 创建时间
  updated_at: Date           // 更新时间
}
```

## 3. 仓库层设计

### 3.1 StatisticsRepository 类

`StatisticsRepository` 类继承自 `BaseRepository`，负责封装与统计相关的数据访问逻辑。

```javascript
class StatisticsRepository extends BaseRepository {
  constructor() {
    super(null); // 不绑定特定模型，因为会操作多个模型
  }
  
  // 方法定义...
}
```

### 3.2 方法定义

#### 3.2.1 获取学习活动统计

```javascript
/**
 * 获取用户的学习活动统计
 * @param {string} userId - 用户ID
 * @returns {Promise<Array>} 活动统计数组
 */
async getLearningActivityStats(userId) {
  // 实现...
}
```

#### 3.2.2 获取每日记录

```javascript
/**
 * 获取用户的每日学习记录
 * @param {string} userId - 用户ID
 * @param {Date} startDate - 开始日期（可选）
 * @param {Date} endDate - 结束日期（可选）
 * @param {number} limit - 限制数量（可选）
 * @param {number} offset - 偏移量（可选）
 * @returns {Promise<Object>} 每日记录和分页信息
 */
async getDailyRecords(userId, startDate, endDate, limit, offset) {
  // 实现...
}
```

#### 3.2.3 获取或创建当日记录

```javascript
/**
 * 获取或创建用户当日记录
 * @param {string} userId - 用户ID
 * @param {Date} date - 日期（默认为今天）
 * @param {Object} transaction - 事务对象（可选）
 * @returns {Promise<Object>} 每日记录
 */
async getOrCreateDailyRecord(userId, date = new Date(), transaction = null) {
  // 实现...
}
```

#### 3.2.4 更新每日记录

```javascript
/**
 * 更新用户每日记录
 * @param {number} recordId - 记录ID
 * @param {Object} updateData - 更新数据
 * @param {Object} transaction - 事务对象（可选）
 * @returns {Promise<Object>} 更新后的记录
 */
async updateDailyRecord(recordId, updateData, transaction = null) {
  // 实现...
}
```

#### 3.2.5 记录学习活动

```javascript
/**
 * 记录学习活动
 * @param {Object} activityData - 活动数据
 * @param {Object} transaction - 事务对象（可选）
 * @returns {Promise<Object>} 创建的活动记录
 */
async recordActivity(activityData, transaction = null) {
  // 实现...
}
```

#### 3.2.6 获取学习活动列表

```javascript
/**
 * 获取用户的学习活动列表
 * @param {string} userId - 用户ID
 * @param {Object} filters - 过滤条件
 * @param {number} page - 页码
 * @param {number} pageSize - 每页记录数
 * @returns {Promise<Object>} 活动列表和分页信息
 */
async getLearningActivities(userId, filters = {}, page = 1, pageSize = 20) {
  // 实现...
}
```

#### 3.2.7 获取学习计划统计

```javascript
/**
 * 获取用户的学习计划统计
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 学习计划统计
 */
async getLearningPlanStats(userId) {
  // 实现...
}
```

#### 3.2.8 获取学习连续天数

```javascript
/**
 * 获取用户的学习连续天数
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 当前连续天数和最长连续天数
 */
async getLearningStreaks(userId) {
  // 实现...
}
```

### 3.3 查询优化策略

1. **索引优化**：
   - 为`LearningActivity`表的`user_id`和`activity_type`字段添加索引
   - 为`DailyRecord`表的`user_id`和`date`字段添加索引
   - 为经常组合查询的字段添加复合索引

2. **查询优化**：
   - 使用`findAll`代替多次`findOne`查询
   - 使用`attributes`限制返回字段
   - 使用`include`减少查询次数
   - 使用`group by`和聚合函数进行统计

3. **分页优化**：
   - 实现高效的分页查询
   - 使用`limit`和`offset`控制返回数量
   - 考虑使用游标分页提高性能

4. **缓存策略**：
   - 缓存不经常变化的统计数据
   - 使用Redis存储热点数据
   - 实现缓存失效机制

## 4. 服务层设计

### 4.1 StatisticsService 类

`StatisticsService` 类负责封装与统计相关的业务逻辑，使用依赖注入获取仓库实例。

```javascript
class StatisticsService {
  constructor(statisticsRepository) {
    this.statisticsRepository = statisticsRepository;
  }
  
  // 方法定义...
}
```

### 4.2 方法定义

#### 4.2.1 获取学习统计数据

```javascript
/**
 * 获取用户的学习统计数据
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 学习统计数据
 */
async getLearningStatistics(userId) {
  // 实现...
}
```

#### 4.2.2 获取每日学习记录

```javascript
/**
 * 获取用户的每日学习记录
 * @param {string} userId - 用户ID
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 每日记录和分页信息
 */
async getDailyRecords(userId, params = {}) {
  // 实现...
}
```

#### 4.2.3 记录学习活动

```javascript
/**
 * 记录用户的学习活动
 * @param {string} userId - 用户ID
 * @param {string} activityType - 活动类型
 * @param {Object} activityData - 活动数据
 * @returns {Promise<Object>} 创建的活动记录
 */
async recordLearningActivity(userId, activityType, activityData = {}) {
  // 实现...
}
```

#### 4.2.4 获取学习活动列表

```javascript
/**
 * 获取用户的学习活动列表
 * @param {string} userId - 用户ID
 * @param {Object} filters - 过滤条件
 * @param {number} page - 页码
 * @param {number} pageSize - 每页记录数
 * @returns {Promise<Object>} 活动列表和分页信息
 */
async getLearningActivities(userId, filters = {}, page = 1, pageSize = 20) {
  // 实现...
}
```

#### 4.2.5 获取学习概览

```javascript
/**
 * 获取用户的学习概览
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 学习概览数据
 */
async getLearningOverview(userId) {
  // 实现...
}
```

#### 4.2.6 获取学习趋势

```javascript
/**
 * 获取用户的学习趋势
 * @param {string} userId - 用户ID
 * @param {number} days - 天数
 * @returns {Promise<Array>} 学习趋势数据
 */
async getLearningTrend(userId, days = 30) {
  // 实现...
}
```

### 4.3 业务逻辑优化策略

1. **事务管理**：
   - 使用事务确保数据一致性
   - 在记录活动时同时更新每日记录

2. **缓存策略**：
   - 缓存频繁访问的统计数据
   - 实现缓存失效机制
   - 使用缓存减少数据库查询

3. **异步处理**：
   - 考虑使用消息队列异步处理活动记录
   - 减少对用户操作的影响

4. **数据聚合**：
   - 实现高效的数据聚合算法
   - 预计算常用统计数据
   - 使用定时任务更新统计数据

## 5. 控制器层设计

### 5.1 StatisticsV2Controller 类

`StatisticsV2Controller` 类负责处理HTTP请求，调用服务层方法，并格式化响应。

```javascript
class StatisticsV2Controller {
  constructor(statisticsService) {
    this.statisticsService = statisticsService;
  }
  
  // 方法定义...
}
```

### 5.2 方法定义

#### 5.2.1 获取学习统计数据

```javascript
/**
 * 获取学习统计数据
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
async getLearningStatistics(req, res) {
  // 实现...
}
```

#### 5.2.2 获取每日学习记录

```javascript
/**
 * 获取每日学习记录
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
async getDailyRecords(req, res) {
  // 实现...
}
```

#### 5.2.3 记录学习活动

```javascript
/**
 * 记录学习活动
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
async recordLearningActivity(req, res) {
  // 实现...
}
```

#### 5.2.4 获取学习活动列表

```javascript
/**
 * 获取学习活动列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
async getLearningActivities(req, res) {
  // 实现...
}
```

### 5.3 Swagger注释示例

```javascript
/**
 * @swagger
 * /api/v1/statistics/learning:
 *   get:
 *     summary: 获取学习统计数据
 *     description: 获取用户的学习统计数据，包括学习天数、连续天数、完成练习数等
 *     tags: [Statistics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取学习统计数据
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalStudyDays:
 *                       type: integer
 *                       example: 15
 *                     currentStreak:
 *                       type: integer
 *                       example: 3
 *                     longestStreak:
 *                       type: integer
 *                       example: 7
 *                     completedExercises:
 *                       type: integer
 *                       example: 25
 *                     viewedInsights:
 *                       type: integer
 *                       example: 42
 *                     createdNotes:
 *                       type: integer
 *                       example: 10
 *                     totalTimeSpent:
 *                       type: integer
 *                       example: 320
 *                     activePlans:
 *                       type: integer
 *                       example: 2
 *                     completedPlans:
 *                       type: integer
 *                       example: 1
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
```

## 6. 路由层设计

### 6.1 StatisticsV2Routes

```javascript
const express = require('express');
const { body, query } = require('express-validator');
const statisticsV2Controller = require('../controllers/statisticsV2.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

// 获取学习统计数据
router.get(
  '/statistics/learning',
  authMiddleware,
  statisticsV2Controller.getLearningStatistics
);

// 获取每日学习记录
router.get(
  '/statistics/daily',
  authMiddleware,
  [
    query('startDate').optional().isDate().withMessage('开始日期格式无效'),
    query('endDate').optional().isDate().withMessage('结束日期格式无效'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('限制数量必须是1-100之间的整数'),
    query('offset').optional().isInt({ min: 0 }).withMessage('偏移量必须是非负整数'),
    validate
  ],
  statisticsV2Controller.getDailyRecords
);

// 记录学习活动
router.post(
  '/statistics/activities',
  authMiddleware,
  [
    body('activityType').isIn([
      'login', 
      'view_exercise', 
      'complete_exercise', 
      'view_insight', 
      'create_note', 
      'like_note', 
      'comment_note', 
      'bubble_interaction',
      'share_content'
    ]).withMessage('无效的活动类型'),
    body('contentId').optional().isInt().withMessage('内容ID必须是整数'),
    body('contentType').optional().isString().withMessage('内容类型必须是字符串'),
    body('planId').optional().isInt().withMessage('学习计划ID必须是整数'),
    body('duration').optional().isInt({ min: 0 }).withMessage('持续时间必须是非负整数'),
    body('metadata').optional().isObject().withMessage('元数据必须是对象'),
    validate
  ],
  statisticsV2Controller.recordLearningActivity
);

// 获取学习活动列表
router.get(
  '/statistics/activities',
  authMiddleware,
  [
    query('activityType').optional().isIn([
      'login', 
      'view_exercise', 
      'complete_exercise', 
      'view_insight', 
      'create_note', 
      'like_note', 
      'comment_note', 
      'bubble_interaction',
      'share_content'
    ]).withMessage('无效的活动类型'),
    query('startDate').optional().isDate().withMessage('开始日期格式无效'),
    query('endDate').optional().isDate().withMessage('结束日期格式无效'),
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须是1-50之间的整数'),
    validate
  ],
  statisticsV2Controller.getLearningActivities
);

module.exports = router;
```

## 7. 单元测试设计

### 7.1 仓库层测试

```javascript
// statistics.repository.test.js
const StatisticsRepository = require('../../../repositories/statistics.repository');
const { LearningActivity, DailyRecord, LearningPlan, sequelize } = require('../../../models');

// 模拟依赖
jest.mock('../../../models');

describe('StatisticsRepository', () => {
  let statisticsRepository;
  
  beforeEach(() => {
    jest.clearAllMocks();
    statisticsRepository = new StatisticsRepository();
  });
  
  describe('getLearningActivityStats', () => {
    it('should return activity stats for a user', async () => {
      // 测试实现...
    });
    
    it('should handle empty results', async () => {
      // 测试实现...
    });
  });
  
  // 其他方法的测试...
});
```

### 7.2 服务层测试

```javascript
// statistics.service.test.js
const StatisticsService = require('../../../services/statistics.service');
const StatisticsRepository = require('../../../repositories/statistics.repository');

// 模拟依赖
jest.mock('../../../repositories/statistics.repository');

describe('StatisticsService', () => {
  let statisticsService;
  let mockStatisticsRepository;
  
  beforeEach(() => {
    jest.clearAllMocks();
    mockStatisticsRepository = new StatisticsRepository();
    statisticsService = new StatisticsService(mockStatisticsRepository);
  });
  
  describe('getLearningStatistics', () => {
    it('should return learning statistics for a user', async () => {
      // 测试实现...
    });
    
    it('should handle errors', async () => {
      // 测试实现...
    });
  });
  
  // 其他方法的测试...
});
```

## 8. 性能考虑

### 8.1 数据库优化

1. **索引策略**：
   - 为频繁查询的字段添加索引
   - 使用复合索引优化多字段查询
   - 定期分析索引使用情况

2. **查询优化**：
   - 减少不必要的JOIN操作
   - 使用子查询替代复杂JOIN
   - 限制返回字段和记录数量

3. **连接池配置**：
   - 优化连接池大小
   - 设置合理的超时时间
   - 监控连接使用情况

### 8.2 缓存策略

1. **Redis缓存**：
   - 缓存频繁访问的统计数据
   - 设置合理的缓存过期时间
   - 实现缓存预热机制

2. **内存缓存**：
   - 使用Node.js内存缓存小型数据
   - 实现LRU缓存策略
   - 监控内存使用情况

### 8.3 异步处理

1. **消息队列**：
   - 考虑使用消息队列异步处理活动记录
   - 减少对用户操作的影响
   - 实现重试机制

2. **批量处理**：
   - 实现批量插入和更新
   - 使用定时任务处理非实时数据
   - 优化批处理性能

## 9. 安全考虑

1. **数据验证**：
   - 严格验证所有输入参数
   - 使用参数绑定防止SQL注入
   - 实现请求限流

2. **授权检查**：
   - 确保用户只能访问自己的数据
   - 实现细粒度的权限控制
   - 记录敏感操作日志

3. **数据保护**：
   - 敏感数据脱敏
   - 实现数据访问审计
   - 遵循最小权限原则

## 10. 结论

本设计文档详细描述了AIBUBB项目统计模块的仓库层和服务层设计，包括数据模型、方法定义、优化策略和测试设计。通过实现这些设计，统计模块将完全遵循项目的分层架构，提高代码的可维护性和可扩展性，同时确保性能和安全性。

后续步骤包括实现仓库层和服务层，编写单元测试，重构控制器和路由，以及进行性能测试和优化。
