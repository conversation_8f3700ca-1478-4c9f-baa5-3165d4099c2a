# 性能优化指南

为了解决数据加载速度慢的问题，我们已经实施了以下优化措施：

## 数据库优化

### MySQL配置优化

1. **增加缓冲池大小**：
   - 将InnoDB缓冲池从默认值增加到256MB
   - 在 `docker-compose.yml` 的 `command` 中通过 `--innodb-buffer-pool-size=256M` 设置。

2. **启用查询缓存**：
   - (注意：MySQL 8.0 已移除查询缓存功能，此项可能基于旧版本或理解有误。)
   - ~~配置了32MB的查询缓存~~
   - ~~设置查询缓存限制为2MB~~
   - ~~打开了查询缓存~~

3. **InnoDB性能调整**：
   - (注意：以下配置可能依赖 MySQL 镜像默认值或内部配置，未在项目文件中显式设置)
   - 增大日志文件大小到64MB
   - 设置事务提交模式为2（更好的性能）
   - 设置flush_method为O_DIRECT（减少双重缓冲）

4. **增大临时表空间**：
   - (注意：以下配置可能依赖 MySQL 镜像默认值或内部配置，未在项目文件中显式设置)
   - tmp_table_size和max_heap_table_size都设为64MB
   - 减少临时表创建的磁盘I/O

5. **增加连接数限制**：
   - 将max_connections设为1000
   - 在 `docker-compose.yml` 的 `command` 中通过 `--max-connections=1000` 设置。
   - (注意：线程缓存大小未在 `docker-compose.yml` 中显式设置)
   - ~~线程缓存大小设为128~~

6. **启用慢查询日志**：
   - (注意：此配置可能依赖 MySQL 镜像默认值或内部配置，未在项目文件中显式启用)
   - 记录执行时间超过1秒的查询
   - 方便性能调优和排查问题

## API与缓存优化

1. **增加客户端缓存时间**：
   - 默认API缓存从5分钟延长到10分钟
   - 系统默认标签和广场标签缓存24小时
   - 用户相关数据缓存时间延长到60分钟

2. **缓存更多的API端点**：
   - 为`/tags/system/default/tags`和`/square/tags`配置了缓存

## Redis优化

1. **内存管理优化**：
   - 设置最大内存为512MB
   - 使用LRU（最近最少使用）淘汰策略
   - 增加样本数量到10，提高LRU精度

2. **持久化配置**：
   - 使用AOF持久化
   - 设置每秒同步一次，平衡性能和持久性
   - 配置AOF重写参数优化磁盘空间使用

3. **连接优化**：
   - 设置连接超时和TCP保活

## Node.js服务优化

1. **内存配置**：
   - 增加Node.js老生代内存空间至1GB
   - 添加NODE_OPTIONS环境变量

2. **增加资源限制**：
   - CPU限制从1个增加到2个
   - 内存限制从1GB增加到2GB

## 性能监控与分析

1. **数据库慢查询监控**：
   - (注意：如上所述，慢查询日志未在项目文件中显式启用)
   - ~~启用慢查询日志，记录执行时间超过1秒的查询~~
   - ~~日志位置：`/var/log/mysql/mysql-slow.log`~~

2. **Redis缓存监控**：
   - 配置日志级别为notice
   - 日志位置：`/data/redis.log` (在 `redis/redis.conf` 中配置)

## 启用优化配置

所有这些优化已经应用到了Docker容器环境中。使用以下命令可以重启服务应用新的配置：

```bash
./scripts/dev-container.sh
```

## 下一步优化建议

如果性能仍然不理想，可以考虑以下措施：

1. **增加指标采集**：
   - 添加APM（应用性能监控）
   - 实现更详细的API性能日志

2. **代码优化**：
   - 检查是否存在N+1查询问题
   - 优化数据库索引
   - 引入数据预加载机制

3. **考虑分布式架构**：
   - 读写分离
   - 分片和水平扩展

4. **CDN与静态资源优化**：
   - 使用CDN分发静态资源
   - 资源压缩和合并 