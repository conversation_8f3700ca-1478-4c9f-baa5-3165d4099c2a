[mysqld]
# 性能优化设置

# InnoDB设置
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 连接设置
max_connections = 1000
thread_cache_size = 128

# 临时表设置
tmp_table_size = 64M
max_heap_table_size = 64M

# 查询优化
join_buffer_size = 4M
sort_buffer_size = 4M
read_buffer_size = 2M
read_rnd_buffer_size = 4M

# 索引优化
key_buffer_size = 32M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/mysql-slow.log
long_query_time = 1

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'
character-set-client-handshake = FALSE

[client]
default-character-set = utf8mb4

[mysql]
default-character-set = utf8mb4