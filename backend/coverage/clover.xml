<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1748347065640" clover="3.2.0">
  <project timestamp="1748347065640" name="All files">
    <metrics statements="2498" coveredstatements="288" conditionals="902" coveredconditionals="163" methods="268" coveredmethods="51" elements="3668" coveredelements="502" complexity="0" loc="2498" ncloc="2498" packages="6" files="97" classes="97"/>
    <package name="controllers">
      <metrics statements="974" coveredstatements="34" conditionals="325" coveredconditionals="7" methods="106" coveredmethods="4"/>
      <file name="ai.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/ai.controller.js">
        <metrics statements="45" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
      </file>
      <file name="batchOperation.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/batchOperation.controller.js">
        <metrics statements="93" coveredstatements="0" conditionals="38" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="88" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="122" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="152" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="183" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="214" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="245" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
      </file>
      <file name="cleanup.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/cleanup.controller.js">
        <metrics statements="50" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="62" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
      </file>
      <file name="dailyContentV2.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/dailyContentV2.controller.js">
        <metrics statements="66" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
      </file>
      <file name="deadLetterQueue.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/deadLetterQueue.controller.js">
        <metrics statements="50" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
      </file>
      <file name="errorMonitor.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/errorMonitor.controller.js">
        <metrics statements="28" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
      </file>
      <file name="exerciseV2.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/exerciseV2.controller.js">
        <metrics statements="88" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="308" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="309" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="395" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="398" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="424" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="425" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
      </file>
      <file name="health.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/health.controller.js">
        <metrics statements="84" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="242" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="283" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="286" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
      </file>
      <file name="insightV2.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/insightV2.controller.js">
        <metrics statements="85" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="289" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="378" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="402" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="403" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
      </file>
      <file name="learningPlanV2.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/learningPlanV2.controller.js">
        <metrics statements="68" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="27" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
      </file>
      <file name="mockData.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/mockData.controller.js">
        <metrics statements="15" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
      </file>
      <file name="noteV2.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/noteV2.controller.js">
        <metrics statements="111" coveredstatements="0" conditionals="47" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="168" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="211" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="240" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="304" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="365" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="366" count="0" type="stmt"/>
        <line num="369" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="370" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="448" count="0" type="stmt"/>
        <line num="449" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="452" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="491" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="501" count="0" type="stmt"/>
        <line num="510" count="0" type="stmt"/>
        <line num="511" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="514" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="525" count="0" type="stmt"/>
      </file>
      <file name="squareV2.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/squareV2.controller.js">
        <metrics statements="29" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="79" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
      </file>
      <file name="statisticsMonitor.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/statisticsMonitor.controller.js">
        <metrics statements="37" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
      </file>
      <file name="statisticsV2.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/statisticsV2.controller.js">
        <metrics statements="44" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
      </file>
      <file name="tagV2.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/tagV2.controller.js">
        <metrics statements="34" coveredstatements="34" conditionals="10" coveredconditionals="7" methods="4" coveredmethods="4"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="3" type="stmt"/>
        <line num="55" count="3" type="stmt"/>
        <line num="56" count="3" type="stmt"/>
        <line num="59" count="3" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="65" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="66" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="4" type="stmt"/>
        <line num="117" count="4" type="stmt"/>
        <line num="118" count="4" type="stmt"/>
        <line num="121" count="4" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="127" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="128" count="1" type="stmt"/>
        <line num="131" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="132" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="2" type="stmt"/>
        <line num="197" count="2" type="stmt"/>
        <line num="198" count="2" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="2" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="231" count="1" type="stmt"/>
        <line num="278" count="1" type="stmt"/>
      </file>
      <file name="themeV2.controller.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/controllers/themeV2.controller.js">
        <metrics statements="47" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
      </file>
    </package>
    <package name="middlewares">
      <metrics statements="43" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="errorHandler.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/middlewares/errorHandler.js">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
      </file>
      <file name="index.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/middlewares/index.js">
        <metrics statements="11" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
      </file>
      <file name="security.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/middlewares/security.js">
        <metrics statements="29" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
      </file>
    </package>
    <package name="models">
      <metrics statements="240" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="DeadLetterQueue.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/DeadLetterQueue.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
      </file>
      <file name="achievement.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/achievement.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
      </file>
      <file name="bContent.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/bContent.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
      </file>
      <file name="badge.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/badge.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
      </file>
      <file name="bubbleContent.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/bubbleContent.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
      </file>
      <file name="bubbleInteraction.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/bubbleInteraction.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
      </file>
      <file name="commentLike.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/commentLike.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
      </file>
      <file name="dailyContent.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/dailyContent.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
      </file>
      <file name="dailyContentRelation.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/dailyContentRelation.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
      </file>
      <file name="dailyRecord.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/dailyRecord.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
      </file>
      <file name="exercise.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/exercise.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
      </file>
      <file name="featureFlag.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/featureFlag.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
      </file>
      <file name="index.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/index.js">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
      </file>
      <file name="insight.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/insight.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
      </file>
      <file name="learningActivity.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/learningActivity.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
      </file>
      <file name="learningPlan.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/learningPlan.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
      </file>
      <file name="learningTemplate.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/learningTemplate.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
      </file>
      <file name="level.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/level.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
      </file>
      <file name="note.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/note.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
      </file>
      <file name="noteComment.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/noteComment.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
      </file>
      <file name="noteLike.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/noteLike.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
      </file>
      <file name="notification.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/notification.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
      </file>
      <file name="passwordResetToken.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/passwordResetToken.model.js">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
      </file>
      <file name="planTag.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/planTag.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
      </file>
      <file name="systemConfig.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/systemConfig.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
      </file>
      <file name="tag.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/tag.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
      </file>
      <file name="tagCategory.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/tagCategory.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
      </file>
      <file name="tagFeedback.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/tagFeedback.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
      </file>
      <file name="tagLike.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/tagLike.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
      </file>
      <file name="tagSynonym.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/tagSynonym.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
      </file>
      <file name="theme.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/theme.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
      </file>
      <file name="unified-index.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/unified-index.js">
        <metrics statements="79" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
      </file>
      <file name="user.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/user.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
      </file>
      <file name="userAchievement.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/userAchievement.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
      </file>
      <file name="userBadge.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/userBadge.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
      </file>
      <file name="userContentProgress.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/userContentProgress.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
      </file>
      <file name="userFeatureAccess.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/userFeatureAccess.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
      </file>
      <file name="userFollow.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/userFollow.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
      </file>
      <file name="userLearningStats.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/userLearningStats.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
      </file>
      <file name="userNotificationSetting.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/userNotificationSetting.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
      </file>
      <file name="userSetting.model.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/models/userSetting.model.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
      </file>
    </package>
    <package name="routes">
      <metrics statements="325" coveredstatements="0" conditionals="46" coveredconditionals="0" methods="14" coveredmethods="0"/>
      <file name="ai.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/ai.routes.js">
        <metrics statements="11" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
      </file>
      <file name="batchOperation.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/batchOperation.routes.js">
        <metrics statements="13" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
      </file>
      <file name="cleanup.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/cleanup.routes.js">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
      </file>
      <file name="dailyContentV2.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/dailyContentV2.routes.js">
        <metrics statements="11" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
      </file>
      <file name="deadLetterQueue.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/deadLetterQueue.routes.js">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
      </file>
      <file name="errorMonitor.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/errorMonitor.routes.js">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
      </file>
      <file name="exerciseV2.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/exerciseV2.routes.js">
        <metrics statements="15" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
      </file>
      <file name="health.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/health.js">
        <metrics statements="75" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="240" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
      </file>
      <file name="health.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/health.routes.js">
        <metrics statements="53" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="226" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
      </file>
      <file name="insightV2.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/insightV2.routes.js">
        <metrics statements="15" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
      </file>
      <file name="learningPlanV2.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/learningPlanV2.routes.js">
        <metrics statements="13" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
      </file>
      <file name="mockData.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/mockData.routes.js">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
      </file>
      <file name="noteV2.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/noteV2.routes.js">
        <metrics statements="19" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
      </file>
      <file name="squareV2.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/squareV2.routes.js">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
      </file>
      <file name="statisticsMonitor.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/statisticsMonitor.routes.js">
        <metrics statements="12" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
      </file>
      <file name="statisticsV2.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/statisticsV2.routes.js">
        <metrics statements="17" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="531" count="0" type="stmt"/>
        <line num="541" count="0" type="stmt"/>
      </file>
      <file name="tagV2.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/tagV2.routes.js">
        <metrics statements="11" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
      </file>
      <file name="testData.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/testData.routes.js">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
      </file>
      <file name="themeV2.routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/routes/themeV2.routes.js">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
      </file>
    </package>
    <package name="services">
      <metrics statements="296" coveredstatements="0" conditionals="151" coveredconditionals="0" methods="42" coveredmethods="0"/>
      <file name="learningPlanV2.service.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/services/learningPlanV2.service.js">
        <metrics statements="222" coveredstatements="0" conditionals="131" coveredconditionals="0" methods="29" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="63" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="204" count="0" type="stmt"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="209" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="260" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="308" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="341" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="342" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="361" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="388" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="407" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="408" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="415" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="435" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="436" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="441" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="442" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="443" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="444" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="447" count="0" type="stmt"/>
        <line num="449" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="452" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="453" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="473" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="474" count="0" type="stmt"/>
        <line num="478" count="0" type="stmt"/>
        <line num="480" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="483" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="484" count="0" type="stmt"/>
        <line num="486" count="0" type="stmt"/>
        <line num="497" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="503" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="504" count="0" type="stmt"/>
        <line num="508" count="0" type="stmt"/>
        <line num="510" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="513" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="514" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="534" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="535" count="0" type="stmt"/>
        <line num="538" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="539" count="0" type="stmt"/>
        <line num="543" count="0" type="stmt"/>
        <line num="545" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="548" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="549" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="563" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="565" count="0" type="stmt"/>
        <line num="568" count="0" type="stmt"/>
        <line num="587" count="0" type="stmt"/>
        <line num="597" count="0" type="stmt"/>
        <line num="607" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="619" count="0" type="stmt"/>
        <line num="630" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="631" count="0" type="stmt"/>
        <line num="635" count="0" type="stmt"/>
        <line num="648" count="0" type="stmt"/>
        <line num="654" count="0" type="stmt"/>
        <line num="662" count="0" type="stmt"/>
        <line num="683" count="0" type="stmt"/>
        <line num="695" count="0" type="stmt"/>
        <line num="697" count="0" type="stmt"/>
        <line num="698" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="699" count="0" type="stmt"/>
        <line num="701" count="0" type="stmt"/>
        <line num="711" count="0" type="stmt"/>
        <line num="713" count="0" type="stmt"/>
        <line num="715" count="0" type="stmt"/>
        <line num="720" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="721" count="0" type="stmt"/>
        <line num="722" count="0" type="stmt"/>
        <line num="726" count="0" type="stmt"/>
        <line num="742" count="0" type="stmt"/>
        <line num="753" count="0" type="stmt"/>
        <line num="754" count="0" type="stmt"/>
        <line num="755" count="0" type="stmt"/>
        <line num="766" count="0" type="stmt"/>
        <line num="767" count="0" type="stmt"/>
        <line num="778" count="0" type="stmt"/>
        <line num="784" count="0" type="stmt"/>
        <line num="785" count="0" type="stmt"/>
        <line num="794" count="0" type="stmt"/>
        <line num="795" count="0" type="stmt"/>
        <line num="797" count="0" type="stmt"/>
        <line num="799" count="0" type="stmt"/>
        <line num="800" count="0" type="stmt"/>
        <line num="801" count="0" type="stmt"/>
        <line num="811" count="0" type="stmt"/>
        <line num="813" count="0" type="stmt"/>
        <line num="815" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="816" count="0" type="stmt"/>
        <line num="820" count="0" type="stmt"/>
        <line num="827" count="0" type="stmt"/>
        <line num="828" count="0" type="stmt"/>
        <line num="829" count="0" type="stmt"/>
        <line num="831" count="0" type="stmt"/>
        <line num="832" count="0" type="stmt"/>
        <line num="833" count="0" type="stmt"/>
        <line num="834" count="0" type="stmt"/>
        <line num="835" count="0" type="stmt"/>
        <line num="838" count="0" type="stmt"/>
        <line num="840" count="0" type="stmt"/>
        <line num="841" count="0" type="stmt"/>
        <line num="852" count="0" type="stmt"/>
        <line num="853" count="0" type="stmt"/>
        <line num="855" count="0" type="stmt"/>
        <line num="856" count="0" type="stmt"/>
        <line num="861" count="0" type="stmt"/>
      </file>
      <file name="tag.service.compatibility.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/services/tag.service.compatibility.js">
        <metrics statements="74" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
      </file>
    </package>
    <package name="utils">
      <metrics statements="620" coveredstatements="254" conditionals="364" coveredconditionals="156" methods="100" coveredmethods="47"/>
      <file name="apiError.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/apiError.js">
        <metrics statements="10" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
      </file>
      <file name="apiResponse.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/apiResponse.js">
        <metrics statements="30" coveredstatements="21" conditionals="21" coveredconditionals="19" methods="10" coveredmethods="10"/>
        <line num="6" count="1" type="cond" truecount="2" falsecount="1"/>
        <line num="8" count="3" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="17" count="1" type="cond" truecount="4" falsecount="0"/>
        <line num="19" count="3" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="31" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="47" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="61" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="75" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="89" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="103" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="117" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
      </file>
      <file name="dataTransformer.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/dataTransformer.js">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="11" count="5" type="stmt"/>
        <line num="14" count="5" type="stmt"/>
        <line num="17" count="5" type="stmt"/>
        <line num="20" count="12" type="stmt"/>
        <line num="21" count="8" type="stmt"/>
      </file>
      <file name="enhanced-data-transformer.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/enhanced-data-transformer.js">
        <metrics statements="73" coveredstatements="49" conditionals="70" coveredconditionals="36" methods="17" coveredmethods="11"/>
        <line num="11" count="5" type="stmt"/>
        <line num="31" count="5" type="stmt"/>
        <line num="33" count="50" type="cond" truecount="4" falsecount="0"/>
        <line num="36" count="49" type="stmt"/>
        <line num="37" count="34" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="53" count="5" type="stmt"/>
        <line num="55" count="35" type="cond" truecount="4" falsecount="0"/>
        <line num="57" count="34" type="stmt"/>
        <line num="79" count="5" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="83" count="0" type="stmt"/>
        <line num="105" count="5" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="110" count="0" type="stmt"/>
        <line num="125" count="5" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="129" count="0" type="stmt"/>
        <line num="142" count="5" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="178" count="5" type="stmt"/>
        <line num="180" count="71" type="cond" truecount="3" falsecount="1"/>
        <line num="182" count="71" type="cond" truecount="2" falsecount="4"/>
        <line num="184" count="43" type="stmt"/>
        <line num="186" count="28" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="207" count="5" type="stmt"/>
        <line num="208" count="39" type="cond" truecount="5" falsecount="0"/>
        <line num="209" count="10" type="stmt"/>
        <line num="212" count="29" type="cond" truecount="2" falsecount="0"/>
        <line num="213" count="4" type="stmt"/>
        <line num="216" count="27" type="stmt"/>
        <line num="217" count="27" type="stmt"/>
        <line num="218" count="71" type="stmt"/>
        <line num="219" count="71" type="stmt"/>
        <line num="222" count="71" type="cond" truecount="4" falsecount="0"/>
        <line num="223" count="10" type="stmt"/>
        <line num="225" count="61" type="stmt"/>
        <line num="229" count="27" type="stmt"/>
        <line num="242" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="243" count="5" type="stmt"/>
        <line num="251" count="5" type="stmt"/>
        <line num="253" count="5" type="stmt"/>
        <line num="255" count="5" type="stmt"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="257" count="0" type="stmt"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="262" count="5" type="cond" truecount="1" falsecount="1"/>
        <line num="263" count="0" type="stmt"/>
        <line num="267" count="5" type="cond" truecount="4" falsecount="0"/>
        <line num="268" count="2" type="stmt"/>
        <line num="272" count="5" type="stmt"/>
        <line num="275" count="5" type="stmt"/>
        <line num="277" count="4" type="cond" truecount="4" falsecount="0"/>
        <line num="278" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="280" count="2" type="stmt"/>
        <line num="283" count="1" type="stmt"/>
        <line num="288" count="4" type="stmt"/>
        <line num="291" count="4" type="stmt"/>
        <line num="294" count="5" type="stmt"/>
        <line num="298" count="5" type="stmt"/>
      </file>
      <file name="enhanced-jwt.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/enhanced-jwt.js">
        <metrics statements="84" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="168" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="210" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
      </file>
      <file name="errorHandler.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/errorHandler.js">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="11" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
      </file>
      <file name="errorMonitor.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/errorMonitor.js">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
      </file>
      <file name="html-sanitizer.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/html-sanitizer.js">
        <metrics statements="32" coveredstatements="0" conditionals="25" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
      </file>
      <file name="index.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/index.js">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
      </file>
      <file name="register-version-routes.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/register-version-routes.js">
        <metrics statements="18" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
      </file>
      <file name="safe-fs.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/safe-fs.js">
        <metrics statements="44" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
      </file>
      <file name="safe-object.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/safe-object.js">
        <metrics statements="52" coveredstatements="0" conditionals="61" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="132" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
      </file>
      <file name="transaction-manager.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/transaction-manager.js">
        <metrics statements="44" coveredstatements="43" conditionals="12" coveredconditionals="6" methods="7" coveredmethods="7"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="29" count="13" type="stmt"/>
        <line num="32" count="13" type="stmt"/>
        <line num="39" count="13" type="stmt"/>
        <line num="40" count="39" type="cond" truecount="2" falsecount="0"/>
        <line num="41" count="24" type="stmt"/>
        <line num="46" count="13" type="stmt"/>
        <line num="47" count="13" type="stmt"/>
        <line num="49" count="13" type="stmt"/>
        <line num="51" count="13" type="stmt"/>
        <line num="54" count="10" type="stmt"/>
        <line num="57" count="10" type="stmt"/>
        <line num="58" count="10" type="stmt"/>
        <line num="60" count="10" type="stmt"/>
        <line num="63" count="3" type="stmt"/>
        <line num="64" count="3" type="stmt"/>
        <line num="65" count="3" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="71" count="3" type="stmt"/>
        <line num="72" count="3" type="stmt"/>
        <line num="75" count="3" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="97" count="2" type="stmt"/>
        <line num="98" count="2" type="stmt"/>
        <line num="99" count="2" type="stmt"/>
        <line num="100" count="5" type="stmt"/>
        <line num="101" count="4" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="121" count="3" type="stmt"/>
        <line num="123" count="3" type="stmt"/>
        <line num="129" count="3" type="stmt"/>
        <line num="130" count="7" type="stmt"/>
        <line num="132" count="7" type="stmt"/>
        <line num="133" count="7" type="stmt"/>
        <line num="134" count="12" type="stmt"/>
        <line num="135" count="12" type="stmt"/>
        <line num="136" count="10" type="stmt"/>
        <line num="138" count="2" type="stmt"/>
        <line num="140" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="141" count="1" type="stmt"/>
        <line num="148" count="2" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
      </file>
      <file name="unified-error.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/unified-error.js">
        <metrics statements="187" coveredstatements="133" conditionals="119" coveredconditionals="95" methods="28" coveredmethods="17"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="20" type="cond" truecount="1" falsecount="1"/>
        <line num="38" count="0" type="stmt"/>
        <line num="56" count="23" type="stmt"/>
        <line num="57" count="23" type="stmt"/>
        <line num="58" count="23" type="stmt"/>
        <line num="59" count="23" type="stmt"/>
        <line num="60" count="23" type="stmt"/>
        <line num="61" count="23" type="stmt"/>
        <line num="62" count="23" type="stmt"/>
        <line num="74" count="1" type="cond" truecount="3" falsecount="0"/>
        <line num="75" count="2" type="stmt"/>
        <line num="85" count="1" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="20" type="stmt"/>
        <line num="90" count="20" type="cond" truecount="1" falsecount="1"/>
        <line num="91" count="20" type="cond" truecount="2" falsecount="0"/>
        <line num="94" count="20" type="cond" truecount="2" falsecount="0"/>
        <line num="95" count="20" type="cond" truecount="2" falsecount="0"/>
        <line num="98" count="20" type="cond" truecount="3" falsecount="0"/>
        <line num="99" count="20" type="cond" truecount="2" falsecount="0"/>
        <line num="102" count="20" type="stmt"/>
        <line num="103" count="20" type="stmt"/>
        <line num="117" count="20" type="stmt"/>
        <line num="120" count="20" type="cond" truecount="1" falsecount="1"/>
        <line num="121" count="0" type="stmt"/>
        <line num="125" count="20" type="stmt"/>
        <line num="126" count="20" type="stmt"/>
        <line num="127" count="20" type="stmt"/>
        <line num="128" count="20" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="20" type="stmt"/>
        <line num="142" count="20" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="215" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="216" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="219" count="7" type="stmt"/>
        <line num="221" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="222" count="4" type="stmt"/>
        <line num="226" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="227" count="1" type="stmt"/>
        <line num="231" count="7" type="stmt"/>
        <line num="247" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="249" count="2" type="stmt"/>
        <line num="251" count="2" type="stmt"/>
        <line num="253" count="2" type="stmt"/>
        <line num="269" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="270" count="2" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="274" count="1" type="stmt"/>
        <line num="279" count="2" type="stmt"/>
        <line num="280" count="2" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="298" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="299" count="4" type="stmt"/>
        <line num="300" count="4" type="stmt"/>
        <line num="301" count="4" type="stmt"/>
        <line num="302" count="4" type="stmt"/>
        <line num="305" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="306" count="1" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="308" count="1" type="stmt"/>
        <line num="309" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="313" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="314" count="1" type="stmt"/>
        <line num="315" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="317" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="318" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="320" count="1" type="stmt"/>
        <line num="321" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="328" count="4" type="stmt"/>
        <line num="331" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="334" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="335" count="4" type="stmt"/>
        <line num="337" count="4" type="stmt"/>
        <line num="348" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="349" count="1" type="stmt"/>
        <line num="350" count="1" type="stmt"/>
        <line num="351" count="1" type="stmt"/>
        <line num="352" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="355" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="356" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="358" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="367" count="1" type="stmt"/>
        <line num="370" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="373" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="374" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="375" count="1" type="stmt"/>
        <line num="378" count="1" type="stmt"/>
        <line num="388" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="389" count="2" type="stmt"/>
        <line num="392" count="2" type="stmt"/>
        <line num="395" count="2" type="stmt"/>
        <line num="397" count="2" type="stmt"/>
        <line num="407" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="408" count="2" type="stmt"/>
        <line num="411" count="2" type="stmt"/>
        <line num="414" count="2" type="stmt"/>
        <line num="416" count="2" type="stmt"/>
        <line num="427" count="1" type="cond" truecount="3" falsecount="0"/>
        <line num="428" count="2" type="stmt"/>
        <line num="431" count="2" type="stmt"/>
        <line num="434" count="2" type="stmt"/>
        <line num="436" count="2" type="stmt"/>
        <line num="447" count="1" type="cond" truecount="3" falsecount="0"/>
        <line num="448" count="2" type="stmt"/>
        <line num="451" count="2" type="stmt"/>
        <line num="454" count="2" type="stmt"/>
        <line num="456" count="2" type="stmt"/>
        <line num="466" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="467" count="2" type="stmt"/>
        <line num="470" count="2" type="stmt"/>
        <line num="473" count="2" type="stmt"/>
        <line num="475" count="2" type="stmt"/>
        <line num="486" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="487" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="488" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="489" count="1" type="stmt"/>
        <line num="493" count="1" type="stmt"/>
        <line num="501" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="503" count="1" type="stmt"/>
        <line num="510" count="1" type="stmt"/>
        <line num="511" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="525" count="1" type="stmt"/>
        <line num="526" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="529" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="531" count="0" type="stmt"/>
        <line num="533" count="0" type="stmt"/>
        <line num="535" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="538" count="0" type="stmt"/>
        <line num="539" count="0" type="stmt"/>
        <line num="543" count="0" type="stmt"/>
        <line num="546" count="1" type="stmt"/>
      </file>
      <file name="wechat.js" path="/Users/<USER>/xiangmu/AIBUBB/backend/utils/wechat.js">
        <metrics statements="27" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
