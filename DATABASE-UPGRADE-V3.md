# AIBUBB 数据库 V3 升级指南

本文档提供了将 AIBUBB 数据库从当前版本升级到 V3 规范的详细步骤。

## 升级内容

V3 版本的主要变更包括：

1. **命名规范统一**：所有表名和字段名统一为 `snake_case`
2. **主键策略优化**：用户表主键从 `VARCHAR(32)` 改为 `BIGINT AUTO_INCREMENT`
3. **软删除机制**：添加 `deleted_at` 字段支持软删除
4. **JSON 字段拆分**：将核心 JSON 字段拆分为独立表
5. **新增功能表**：添加游戏化元素、社区互动、学习追踪等新表

## 升级前准备

1. **备份数据库**：升级前必须备份数据库，以便在出现问题时能够恢复
2. **停止应用服务**：升级过程中应停止所有连接到数据库的应用服务
3. **测试环境验证**：建议先在测试环境执行升级脚本，验证无误后再在生产环境执行

## 升级步骤

### 1. 准备升级脚本

确保以下文件已存在于项目中：

- `complete_migration_v3.sql`：完整的 SQL 升级脚本
- `backend/migrations/20240503_upgrade_to_v3.migration.js`：迁移脚本
- `backend/scripts/backup_database.js`：数据库备份脚本
- `backend/scripts/run_v3_migration.js`：迁移执行脚本
- `backend/scripts/execute_v3_upgrade.js`：完整升级执行脚本

### 2. 执行升级

有两种方式执行升级：

#### 方式一：使用自动化脚本（推荐）

执行以下命令：

```bash
node backend/scripts/execute_v3_upgrade.js
```

此脚本会自动执行以下步骤：
1. 备份当前数据库
2. 执行 V3 升级迁移

#### 方式二：手动执行各步骤

如果您希望更精细地控制升级过程，可以分步执行：

1. 备份数据库：

```bash
node backend/scripts/backup_database.js
```

2. 执行迁移：

```bash
node backend/scripts/run_v3_migration.js
```

### 3. 验证升级结果

升级完成后，请执行以下验证：

1. 检查数据库表结构是否符合 V3 规范
2. 验证数据是否正确迁移
3. 启动应用并测试各项功能

## 回滚方案

如果升级过程中出现问题，请按以下步骤回滚：

1. 停止所有应用服务
2. 使用备份恢复数据库：

```bash
mysql -h [主机] -P [端口] -u [用户名] -p [数据库名] < [备份文件路径]
```

## 注意事项

1. **不可逆操作**：此升级是不可逆的，一旦执行无法自动回滚
2. **应用代码适配**：升级后需要更新应用代码以适配新的表名和字段名
3. **性能影响**：升级过程可能需要较长时间，请在低峰期执行
4. **权限要求**：执行升级的数据库用户需要有 DDL 权限

## 技术支持

如果在升级过程中遇到问题，请联系技术支持团队。
