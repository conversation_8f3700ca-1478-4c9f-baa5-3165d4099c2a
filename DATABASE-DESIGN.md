# 以学习计划为核心的数据库设计

## 数据库架构概述

本文档旨在详细描述以学习计划为核心的小程序后端数据库设计。数据库采用关系型结构，通过精心设计的表结构和关联关系，实现了从主题到学习计划再到标签和内容的多层次数据组织。

**重要提示：本文档旨在提供数据库结构概览。所有数据库结构的创建、修改和演变均通过 `backend/migrations/` 目录下的 Sequelize 迁移脚本进行管理。迁移脚本是数据库结构的唯一真实来源 (Single Source of Truth)，本文档仅供参考，可能存在滞后。**

## 核心数据表

### 1. 用户表 (User)

存储用户基本信息和状态。

```sql
CREATE TABLE User (
  id VARCHAR(32) PRIMARY KEY COMMENT '用户唯一标识，使用微信openid或手机号前缀phone_',
  nickname VARCHAR(50) COMMENT '用户昵称',
  avatar_url VARCHAR(255) COMMENT '头像URL',
  gender TINYINT COMMENT '性别：0未知，1男，2女',
  phone VARCHAR(20) UNIQUE COMMENT '手机号',
  password VARCHAR(100) COMMENT '密码哈希',
  login_type ENUM('wechat', 'phone') DEFAULT 'wechat' COMMENT '登录类型：wechat微信登录，phone手机号登录',
  last_login_at DATETIME COMMENT '最后登录时间', -- Sequelize uses DataTypes.DATE
  study_days INT DEFAULT 0 COMMENT '学习天数',
  level INT DEFAULT 1 COMMENT '用户等级',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  INDEX idx_created_at (created_at)
);
```

### 2. 主题表 (Theme)

存储学习主题信息，由超级管理员管理。
(**注意：代码库中未发现管理主题的公开 API，推测主题的创建/更新/删除通过数据库直接操作或内部脚本完成。实际可用主题列表需查询数据库。**)

```sql
CREATE TABLE Theme (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主题ID',
  name VARCHAR(50) NOT NULL COMMENT '中文名称',
  english_name VARCHAR(50) COMMENT '英文名称',
  description TEXT COMMENT '描述',
  icon VARCHAR(50) COMMENT '图标',
  color VARCHAR(20) COMMENT '颜色代码',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  INDEX idx_is_active (is_active)
);
```

### 3. 学习计划表 (LearningPlan)

核心表，存储用户创建的学习计划的基本目标。
(**注意：此表结构与之前版本有较大变化，侧重每日目标而非整体计划细节。**)

```sql
CREATE TABLE LearningPlan (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '计划ID',
  user_id VARCHAR(32) COMMENT '用户ID', -- allowNull: true in model
  theme_id INT COMMENT '主题ID', -- allowNull: true in model
  daily_goal_exercises INT DEFAULT 3 COMMENT '每日练习目标数量',
  daily_goal_insights INT DEFAULT 5 COMMENT '每日观点目标数量',
  daily_goal_time INT DEFAULT 15 COMMENT '每日学习时间目标(分钟)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', -- Model uses DATE, allowNull: true, defaultNOW, timestamps: false
  is_system_default BOOLEAN DEFAULT FALSE COMMENT '是否为系统默认计划', -- Added from migration
  -- 注意：无 updated_at, timestamps: false in model
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE, -- Defined in models/index.js
  FOREIGN KEY (theme_id) REFERENCES Theme(id) ON DELETE SET NULL, -- Defined in models/index.js
  INDEX idx_user_id (user_id),
  INDEX idx_theme_id (theme_id),
  INDEX idx_user_theme (user_id, theme_id),
  INDEX idx_created_at (created_at)
);
```

### 4. 标签表 (Tag)

存储AI生成的标签。
(**注意：Tag 与 LearningPlan 通过 PlanTag 表建立多对多关联。**)

```sql
CREATE TABLE Tag (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
  name VARCHAR(50) NOT NULL COMMENT '标签名称', -- Was VARCHAR(10) 2-4 chars, model says STRING(50)
  category_id INT COMMENT '关联的标签分类ID',
  relevance_score FLOAT DEFAULT 1.0 COMMENT '相关性得分(0-1)',
  weight FLOAT DEFAULT 1.0 COMMENT '权重(0-1)，用于排序和推荐',
  usage_count INT DEFAULT 0 COMMENT '使用次数',
  is_verified BOOLEAN DEFAULT FALSE COMMENT '是否经过验证',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  -- 注意：无 updated_at, updatedAt: false in model
  FOREIGN KEY (category_id) REFERENCES TagCategory(id) ON DELETE SET NULL, -- Defined in models/index.js
  INDEX idx_name (name),
  INDEX idx_category_id (category_id),
  INDEX idx_weight (weight),
  INDEX idx_usage_count (usage_count),
  INDEX idx_is_verified (is_verified)
);
```

**注意：标签与学习计划的关系现在完全通过 PlanTag 表实现多对多关联，Tag 表中不再包含 plan_id 字段。**

### 5. 计划-标签关联表 (PlanTag)

实现 `LearningPlan` 与 `Tag` 之间多对多关系的连接表。

```sql
CREATE TABLE PlanTag (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
  plan_id INT NOT NULL COMMENT '学习计划ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  relevance_score FLOAT DEFAULT 1.0 COMMENT '相关性得分(0-1)',
  weight FLOAT DEFAULT 1.0 COMMENT '权重(0-1)，用于排序和推荐',
  is_primary BOOLEAN DEFAULT FALSE COMMENT '是否为主要标签',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  FOREIGN KEY (plan_id) REFERENCES LearningPlan(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES Tag(id) ON DELETE CASCADE,
  UNIQUE INDEX idx_plan_tag (plan_id, tag_id), -- Ensures uniqueness
  INDEX idx_plan_id (plan_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_is_primary (is_primary)
);
```

### 6. 练习表 (Exercise)

存储练习类内容。(**注意：直接关联到 Tag**)

```sql
CREATE TABLE Exercise (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '练习ID',
  tag_id INT NOT NULL COMMENT '关联的标签ID',
  title VARCHAR(100) NOT NULL COMMENT '练习标题',
  description TEXT NOT NULL COMMENT '练习描述',
  expected_result TEXT COMMENT '预期结果',
  difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度级别',
  time_estimate INT DEFAULT 5 COMMENT '预计完成时间(分钟)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  FOREIGN KEY (tag_id) REFERENCES Tag(id) ON DELETE CASCADE, -- Defined in models/index.js
  INDEX idx_tag_id (tag_id),
  INDEX idx_difficulty (difficulty)
);
```

### 7. 观点表 (Insight)

存储观点类内容。(**注意：直接关联到 Tag**)

```sql
CREATE TABLE Insight (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '观点ID',
  tag_id INT NOT NULL COMMENT '关联的标签ID',
  content TEXT NOT NULL COMMENT '观点内容',
  source VARCHAR(100) COMMENT '来源',
  background TEXT COMMENT '背景解释',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  -- 注意：无 updated_at, updatedAt: false in model
  FOREIGN KEY (tag_id) REFERENCES Tag(id) ON DELETE CASCADE, -- Defined in models/index.js
  INDEX idx_tag_id (tag_id)
);
```

### 8. 笔记表 (Note)

存储笔记类内容。(**注意：直接关联到 Tag 和 User**)

```sql
CREATE TABLE Note (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '笔记ID',
  tag_id INT NOT NULL COMMENT '关联的标签ID',
  user_id VARCHAR(32) COMMENT '创建用户ID(NULL表示AI生成)', -- allowNull: true in model
  title VARCHAR(100) NOT NULL COMMENT '标题',
  content TEXT NOT NULL COMMENT '内容',
  image_url VARCHAR(255) COMMENT '配图URL',
  likes INT DEFAULT 0 COMMENT '点赞数',
  comments INT DEFAULT 0 COMMENT '评论数',
  is_ai_generated BOOLEAN DEFAULT FALSE COMMENT '是否AI生成',
  status ENUM('draft', 'published', 'hidden') DEFAULT 'published' COMMENT '状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  FOREIGN KEY (tag_id) REFERENCES Tag(id) ON DELETE CASCADE, -- Defined in models/index.js
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE SET NULL, -- Defined in models/index.js
  INDEX idx_tag_id (tag_id),
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);
```

### 9. 学习活动表 (LearningActivity)

记录用户的学习行为（简化版）。
(**注意：此表结构与之前版本有较大变化，非常简化。**)

```sql
CREATE TABLE LearningActivity (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '活动ID',
  user_id VARCHAR(32) COMMENT '用户ID', -- allowNull: true in model
  activity_type VARCHAR(50) COMMENT '活动类型', -- allowNull: true in model
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', -- Model uses DATE, allowNull: true, defaultNOW, timestamps: false
  -- 注意：无 updated_at, timestamps: false in model
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE, -- Defined in models/index.js
  INDEX idx_user_id (user_id),
  INDEX idx_activity_type (activity_type),
  INDEX idx_user_activity_date (user_id, activity_type), -- Name from model index
  INDEX idx_created_at (created_at)
);
```

### 10. 泡泡互动记录表 (BubbleInteraction)

记录用户与泡泡的互动。

```sql
CREATE TABLE BubbleInteraction (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '互动ID',
  user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  interaction_type ENUM('click', 'drag', 'view', 'hold') NOT NULL COMMENT '互动类型: 点击、拖拽、查看、长按',
  duration INT DEFAULT 0 COMMENT '互动持续时间(毫秒)',
  position_x FLOAT COMMENT '互动位置X坐标',
  position_y FLOAT COMMENT '互动位置Y坐标',
  device_info JSON COMMENT '设备信息',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE, -- Defined in models/index.js
  FOREIGN KEY (tag_id) REFERENCES Tag(id) ON DELETE CASCADE, -- Defined in models/index.js
  INDEX idx_user_id (user_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_interaction_type (interaction_type),
  INDEX idx_created_at (created_at)
);
```

### 11. 每日学习内容表 (DailyContent)

存储AI生成的学习计划每日内容。

```sql
CREATE TABLE DailyContent (
  id INT AUTO_INCREMENT PRIMARY KEY,
  plan_id INT NOT NULL COMMENT '所属学习计划ID',
  day_number INT NOT NULL COMMENT '天数（第几天）',
  title VARCHAR(100) NOT NULL COMMENT '日内容标题',
  content TEXT NOT NULL COMMENT '日内容详情',
  is_completed BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已完成',
  completion_date DATETIME COMMENT '完成日期',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (plan_id) REFERENCES LearningPlan(id) ON DELETE CASCADE,
  INDEX daily_content_plan_id_idx (plan_id),
  UNIQUE INDEX daily_content_plan_day_idx (plan_id, day_number)
);
```

### 12. 每日学习记录表 (DailyRecord)

记录用户每天的学习统计（简化版）。
(**注意：此表结构与之前版本有较大变化，侧重经验和目标完成状态。**)

```sql
CREATE TABLE DailyRecord (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
  user_id VARCHAR(32) COMMENT '用户ID', -- allowNull: true in model
  exp_earned INT DEFAULT 0 COMMENT '获得的经验值',
  core_completed TINYINT DEFAULT 0 COMMENT '是否完成核心任务 (0: No, 1: Yes)',
  daily_goal_completed TINYINT DEFAULT 0 COMMENT '是否完成每日目标 (0: No, 1: Yes)',
  -- 注意：无 created_at, updated_at, timestamps: false in model
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE, -- Defined in models/index.js
  INDEX idx_user_id (user_id)
);
```

## 辅助与关联数据表

### 13. 标签分类表 (TagCategory)

存储标签的分类信息。
(**注意：此表结构与之前版本有较大变化。**)

```sql
CREATE TABLE TagCategory (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
  name VARCHAR(50) NOT NULL COMMENT '分类名称',
  parent_id INT COMMENT '父分类ID',
  description TEXT COMMENT '描述',
  sort_order INT DEFAULT 0 COMMENT '排序顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  FOREIGN KEY (parent_id) REFERENCES TagCategory(id) ON DELETE SET NULL, -- Self-referencing
  INDEX idx_parent_id (parent_id)
);
```

### 14. 笔记点赞表 (NoteLike)

记录用户对笔记的点赞。

```sql
CREATE TABLE NoteLike (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '点赞ID',
  note_id INT NOT NULL COMMENT '笔记ID',
  user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  -- 注意：无 updated_at, updatedAt: false in model
  FOREIGN KEY (note_id) REFERENCES Note(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE,
  UNIQUE INDEX idx_note_user_like (note_id, user_id),
  INDEX idx_note_id (note_id),
  INDEX idx_user_id (user_id)
);
```

### 15. 标签点赞表 (TagLike)

记录用户对标签的点赞。

```sql
CREATE TABLE TagLike (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '点赞ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  -- 注意：无 updated_at, updatedAt: false in model
  FOREIGN KEY (tag_id) REFERENCES Tag(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE,
  UNIQUE INDEX idx_tag_user_like (tag_id, user_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_user_id (user_id)
);
```

### 16. 笔记评论表 (NoteComment)

存储用户对笔记的评论。

```sql
CREATE TABLE NoteComment (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '评论ID',
  note_id INT NOT NULL COMMENT '笔记ID',
  user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
  content TEXT NOT NULL COMMENT '评论内容',
  parent_comment_id INT COMMENT '回复的父评论ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  FOREIGN KEY (note_id) REFERENCES Note(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE,
  FOREIGN KEY (parent_comment_id) REFERENCES NoteComment(id) ON DELETE CASCADE,
  INDEX idx_note_id (note_id),
  INDEX idx_user_id (user_id),
  INDEX idx_parent_comment_id (parent_comment_id)
);
```

### 17. 用户关注表 (UserFollow)

记录用户之间的关注关系。

```sql
CREATE TABLE UserFollow (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '关注关系ID',
  follower_id VARCHAR(32) NOT NULL COMMENT '关注者ID',
  following_id VARCHAR(32) NOT NULL COMMENT '被关注者ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  -- 注意：无 updated_at, updatedAt: false in model
  FOREIGN KEY (follower_id) REFERENCES User(id) ON DELETE CASCADE,
  FOREIGN KEY (following_id) REFERENCES User(id) ON DELETE CASCADE,
  UNIQUE INDEX idx_user_follow_pair (follower_id, following_id),
  INDEX idx_follower_id (follower_id),
  INDEX idx_following_id (following_id)
);
```

### 18. 标签同义词表 (TagSynonym)

存储标签的同义词。

```sql
CREATE TABLE TagSynonym (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '同义词ID',
  tag_id INT NOT NULL COMMENT '主标签ID',
  synonym VARCHAR(50) NOT NULL COMMENT '同义词名称',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  FOREIGN KEY (tag_id) REFERENCES Tag(id) ON DELETE CASCADE,
  UNIQUE INDEX idx_tag_synonym (tag_id, synonym),
  INDEX idx_tag_id (tag_id),
  INDEX idx_synonym (synonym)
);
```

### 19. 标签反馈表 (TagFeedback)

收集用户对标签质量的反馈。

```sql
CREATE TABLE TagFeedback (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '反馈ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
  feedback_type ENUM('inaccurate', 'irrelevant', 'duplicate', 'offensive', 'other') NOT NULL COMMENT '反馈类型',
  comment TEXT COMMENT '具体说明',
  status ENUM('pending', 'reviewed', 'resolved', 'rejected') DEFAULT 'pending' COMMENT '处理状态',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  FOREIGN KEY (tag_id) REFERENCES Tag(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE,
  INDEX idx_tag_id (tag_id),
  INDEX idx_user_id (user_id),
  INDEX idx_feedback_type (feedback_type),
  INDEX idx_status (status)
);
```

### 20. 泡泡内容表 (BubbleContent)

(推测) 存储泡泡展示的具体内容片段。

```sql
-- Note: Structure based on BubbleContent.model.js
CREATE TABLE BubbleContent (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '内容ID',
  interaction_id INT COMMENT '关联的互动ID (可能用于追踪来源)', -- allowNull: true
  tag_id INT COMMENT '关联的标签ID (可能用于内容分类)', -- allowNull: true
  content_type VARCHAR(50) COMMENT '内容类型 (如: exercise_title, insight_snippet)', -- allowNull: true
  content TEXT NOT NULL COMMENT '实际展示的内容文本',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Managed by Sequelize timestamps
  FOREIGN KEY (interaction_id) REFERENCES BubbleInteraction(id) ON DELETE SET NULL,
  FOREIGN KEY (tag_id) REFERENCES Tag(id) ON DELETE SET NULL,
  INDEX idx_interaction_id (interaction_id),
  INDEX idx_tag_id (tag_id),
  INDEX idx_content_type (content_type)
);
```

### 21. BContent 表 (BContent)

(用途不明，模型简单) 可能是一个通用的内容存储表或测试表。

```sql
-- Note: Structure based on BContent.model.js
CREATE TABLE BContent (
  id INT AUTO_INCREMENT PRIMARY KEY,
  content TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 成就与用户状态相关表

以下表存在于数据库中，但未在原始 `backend/models/index.js` 中加载，推测与成就、等级、用户统计和奖励相关。

### 22. 成就表 (Achievement)

定义可获得的成就。

```sql
-- Note: Structure from describe_table
CREATE TABLE Achievement (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50),
  description TEXT,
  icon_url VARCHAR(255),
  requirement_type VARCHAR(20),
  requirement_value INT,
  exp_reward INT DEFAULT 0,
  is_hidden TINYINT DEFAULT 0,
  created_at DATETIME,
  updated_at DATETIME,
  INDEX idx_requirement_type (requirement_type) -- From describe_table Key: MUL
);
```

### 23. 等级表 (Level)

定义用户等级体系。

```sql
-- Note: Structure inferred, needs actual describe_table if different
CREATE TABLE Level (
  level INT PRIMARY KEY COMMENT '等级',
  exp_required INT NOT NULL COMMENT '达到该等级所需经验',
  title VARCHAR(50) COMMENT '等级称号',
  created_at DATETIME,
  updated_at DATETIME
);
```

### 24. 用户成就表 (UserAchievement)

记录用户获得的成就。

```sql
-- Note: Structure inferred, needs actual describe_table if different
CREATE TABLE UserAchievement (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(32) NOT NULL,
  achievement_id INT NOT NULL,
  achieved_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE,
  FOREIGN KEY (achievement_id) REFERENCES Achievement(id) ON DELETE CASCADE,
  UNIQUE INDEX idx_user_achievement (user_id, achievement_id),
  INDEX idx_user_id (user_id),
  INDEX idx_achievement_id (achievement_id)
);
```

### 25. 用户内容进度表 (UserContentProgress)

记录用户对具体内容（如练习、笔记）的进度。

```sql
-- Note: Structure inferred, needs actual describe_table if different
CREATE TABLE UserContentProgress (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(32) NOT NULL,
  content_type VARCHAR(50) NOT NULL COMMENT '内容类型 (e.g., exercise, note, insight)',
  content_id INT NOT NULL COMMENT '内容ID',
  status ENUM('not_started', 'in_progress', 'completed') DEFAULT 'not_started',
  progress INT DEFAULT 0 COMMENT '进度百分比 (0-100)',
  last_accessed_at DATETIME,
  created_at DATETIME,
  updated_at DATETIME,
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE,
  INDEX idx_user_content (user_id, content_type, content_id),
  INDEX idx_user_id (user_id),
  INDEX idx_content_type (content_type)
);
```

### 26. 用户学习统计表 (UserLearningStats)

可能存储更详细的用户学习统计数据。

```sql
-- Note: Structure inferred, needs actual describe_table if different
CREATE TABLE UserLearningStats (
  user_id VARCHAR(32) PRIMARY KEY,
  total_exp INT DEFAULT 0,
  total_study_time_minutes INT DEFAULT 0,
  completed_exercises INT DEFAULT 0,
  created_notes INT DEFAULT 0,
  streak_days INT DEFAULT 0,
  last_streak_date DATE,
  created_at DATETIME,
  updated_at DATETIME,
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE
);
```

### 27. 用户奖励表 (UserReward)

记录用户获得的奖励（可能与成就、活动相关）。

```sql
-- Note: Structure inferred, needs actual describe_table if different
CREATE TABLE UserReward (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(32) NOT NULL,
  reward_type VARCHAR(50) NOT NULL,
  reward_value VARCHAR(100),
  source_type VARCHAR(50) COMMENT '奖励来源类型 (e.g., achievement, event)',
  source_id VARCHAR(50) COMMENT '奖励来源ID',
  claimed BOOLEAN DEFAULT FALSE,
  created_at DATETIME,
  updated_at DATETIME,
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_reward_type (reward_type),
  INDEX idx_source (source_type, source_id)
);
```

### 28. 用户设置表 (USettings)

存储用户的个性化设置。

```sql
-- Note: Structure inferred, needs actual describe_table if different
CREATE TABLE USettings (
  user_id VARCHAR(32) PRIMARY KEY,
  theme_preference VARCHAR(50) COMMENT '界面主题偏好',
  notification_enabled BOOLEAN DEFAULT TRUE COMMENT '是否接收通知',
  privacy_level ENUM('public', 'followers', 'private') DEFAULT 'public',
  created_at DATETIME,
  updated_at DATETIME,
  FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE
);
```

## 数据库关系图 (ERD)

(建议使用工具如 Mermaid 或 draw.io 等基于上述表结构和关系绘制 ERD，并嵌入或链接在此处)

```mermaid
erDiagram
    User ||--o{ LearningPlan : creates
    Theme ||--o{ LearningPlan : has
    User }o--|| USettings : has
    User ||--o{ Note : creates
    User ||--o{ NoteLike : likes
    User ||--o{ TagLike : likes
    User ||--o{ NoteComment : comments_on
    User ||--o{ UserFollow : follows
    User ||--o{ UserFollow : followed_by
    User ||--o{ LearningActivity : performs
    User ||--o{ DailyRecord : has_daily
    User ||--o{ BubbleInteraction : interacts
    User ||--o{ TagFeedback : gives_feedback_on
    User ||--o{ UserAchievement : earns
    User ||--o{ UserContentProgress : progresses_on
    User ||--o{ UserLearningStats : has_stats
    User ||--o{ UserReward : receives

    LearningPlan ||--|{ PlanTag : includes
    Tag ||--|{ PlanTag : included_in
    PlanTag }o--|| LearningPlan : belongs_to_plan
    PlanTag }o--|| Tag : belongs_to_tag

    Tag ||--o{ Exercise : categorizes
    Tag ||--o{ Insight : categorizes
    Tag ||--o{ Note : categorizes
    TagCategory ||--o{ Tag : categorizes
    TagCategory ||--o{ TagCategory : has_parent
    Tag ||--o{ TagSynonym : has
    Tag ||--o{ TagFeedback : receives_feedback
    Tag ||--o{ BubbleInteraction : related_to

    Note ||--o{ NoteLike : receives_like
    Note ||--o{ NoteComment : receives_comment
    NoteComment ||--o{ NoteComment : replies_to

    BubbleInteraction ||--o{ BubbleContent : generates

    Achievement ||--o{ UserAchievement : awarded_as

    Level -- User : "references (indirectly)"
```

## 索引策略

*   **主键 (PK):** 所有表都有主键，大部分为自增 `INT`，`User` 表为 `VARCHAR(32)`。
*   **外键 (FK):** 所有外键都创建了索引 (`INDEX idx_...`)，以优化 JOIN 查询性能。
*   **唯一约束 (Unique):** 在需要确保唯一性的字段组合上创建了唯一索引（如 `User.phone`, `PlanTag(plan_id, tag_id)`, `UserFollow(follower_id, following_id)`）。
*   **常用查询字段:** 对经常用于 WHERE 条件、ORDER BY 或 GROUP BY 的字段（如 `Tag.name`, `Note.status`, `BubbleInteraction.interaction_type`, `created_at` 等）创建了索引。

索引策略旨在平衡查询性能和写操作开销。未来应根据实际查询日志和性能分析 (如 `EXPLAIN`) 进行调整。

## 结构变更管理

**再次强调：所有数据库结构的变更必须通过 `backend/migrations/` 目录下的 Sequelize 迁移脚本进行管理。** 这确保了变更的版本控制、可重复性和在不同环境间的一致性。请勿直接修改生产数据库结构。