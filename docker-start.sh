#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}开始启动AIBUBB Docker容器...${NC}"

# 检查docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker未安装，请先安装Docker${NC}"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}错误: Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi

# 创建必要的目录
echo -e "${YELLOW}创建必要的目录...${NC}"
mkdir -p backend/logs
mkdir -p mysql/init

# 检查环境变量文件是否存在
if [ ! -f .env ]; then
    echo -e "${RED}错误: 未找到.env文件，请先创建.env文件${NC}"
    echo -e "${YELLOW}提示: 可以复制.env.example为.env，并填入实际值${NC}"
    exit 1
fi

echo -e "${GREEN}使用根目录.env文件作为环境配置...${NC}"

# 启动容器
echo -e "${YELLOW}启动Docker容器...${NC}"
docker-compose up -d

# 检查容器状态
echo -e "${YELLOW}检查容器状态...${NC}"
sleep 5
CONTAINERS=$(docker-compose ps -q)
ALL_RUNNING=true

for CONTAINER in $CONTAINERS; do
    STATUS=$(docker inspect --format='{{.State.Status}}' $CONTAINER)
    if [ "$STATUS" != "running" ]; then
        ALL_RUNNING=false
        CONTAINER_NAME=$(docker inspect --format='{{.Name}}' $CONTAINER | sed 's/\///')
        echo -e "${RED}容器 $CONTAINER_NAME 未正常运行，状态: $STATUS${NC}"
    fi
done

if [ "$ALL_RUNNING" = true ]; then
    echo -e "${GREEN}所有容器已成功启动!${NC}"
    echo -e "${GREEN}API服务运行在: http://localhost:9090${NC}"
    echo -e "${GREEN}API文档地址: http://localhost:9090/api-docs${NC}"
else
    echo -e "${RED}部分容器未能正常启动，请检查日志:${NC}"
    echo -e "${YELLOW}docker-compose logs${NC}"
fi
