# Docker开发指南

本项目使用Docker容器进行开发和部署。为了保持一致性和避免冲突，请遵循以下指南。

## 基本原则

1. **始终使用Docker容器**：所有服务（后端、数据库、Redis）都应通过Docker容器运行
2. **避免本地运行服务**：不要在本地直接启动Node.js服务，以防止端口冲突和数据不一致
3. **统一配置**：确保所有配置指向Docker容器（例如API URL、数据库连接）

## 开发流程

### 初次设置

```bash
# 克隆项目
git clone [项目地址]
cd AIBUBB

# 启动所有服务
docker-compose up -d
```

### 日常开发

1. **前端开发**：
   - 直接修改小程序代码
   - 使用微信开发者工具预览和调试
   - 所有API请求会自动发送到Docker容器中的后端服务

2. **后端开发**：
   - 修改后端代码
   - 使用辅助脚本重建后端容器：
   ```bash
   ./scripts/dev-container.sh
   ```
   - 此脚本会停止旧容器、重新构建并启动新容器

### 容器管理

```bash
# 查看所有容器状态
docker-compose ps

# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 查看后端日志
docker logs aibubb-backend -f

# 查看数据库日志
docker logs aibubb-mysql -f
```

## 注意事项

1. **端口使用**：
   - 后端API：9090
   - MySQL：3306
   - Redis：6379
   
   确保这些端口在本地没有其他服务占用

2. **数据库访问**：
   - 主机：localhost
   - 端口：3306
   - 用户名：aibubb_user
   - 密码：aibubb_password
   - 数据库名：aibubb_db

3. **常见问题**：
   - 如果API无法连接，检查Docker容器是否正在运行：`docker-compose ps`
   - 如果数据库报错，检查数据库容器状态：`docker logs aibubb-mysql`
   - 如果需要执行数据库操作，使用：`docker exec -it aibubb-mysql mysql -uaibubb_user -pYOUR_DB_PASSWORD aibubb_db` (请将 `YOUR_DB_PASSWORD` 替换为你的 .env 文件中的实际密码)

## 配置一致性

为确保开发环境和生产环境的一致性，我们使用以下配置：

1. **微信小程序**：
   - `utils/api.js`中的`BASE_URL`根据环境动态设置，开发环境指向 `http://localhost:9090/api/v1`。

2. **后端服务**：
   - 使用项目根目录的 `.env` 文件配置数据库连接、JWT 密钥等。
   - 服务间通信通过 `docker-compose.yml` 定义的 Docker 网络进行，并可能覆盖 `.env` 中的 `DB_HOST` 和 `REDIS_URL`。

通过遵循这些指南，我们可以确保开发环境的一致性，避免"在我电脑上能运行"的问题。 