# AI模型服务提供商测试指南

本文档提供了如何测试和评估不同AI模型服务提供商的指南。

## 支持的模型服务提供商

目前支持以下三个模型服务提供商：

1. **字节大模型（ByteDance）**
   - `deepseek-r1-250120`
   - `doubao-1-5-thinking-pro-250415`
   - `deepseek-v3-250324`

2. **阿里云百炼（Aliyun）**
   - `qwen-plus-latest`
   - `qwen-plus-2025-04-28`（支持思考模式）

3. **腾讯混元（Hunyuan）**
   - `hunyuan-turbos-latest`
   - `hunyuan-t1-20250403`

## 环境配置

在开始测试之前，请确保已正确配置环境变量：

1. 复制 `.env.example` 为 `.env`：
   ```bash
   cp .env.example .env
   ```

2. 编辑 `.env` 文件，填入各个提供商的API密钥：
   ```
   # 字节大模型API配置
   ARK_API_KEY=your_ark_api_key_here
   ARK_API_MODEL=deepseek-r1-250120

   # 阿里云百炼API配置
   DASHSCOPE_API_KEY=sk-your-aliyun-api-key-here
   DASHSCOPE_API_MODEL=qwen-plus-latest
   DASHSCOPE_ENABLE_THINKING=false

   # 腾讯混元大模型API配置
   HUNYUAN_API_KEY=your_hunyuan_api_key_here
   HUNYUAN_API_MODEL=hunyuan-turbos-latest
   ```

## 测试脚本

### 1. 综合测试

运行综合测试，测试所有提供商和模型类型：

```bash
./run-ai-tests.sh
```

指定特定提供商：

```bash
./run-ai-tests.sh --provider=bytedance
```

指定特定任务：

```bash
./run-ai-tests.sh --task=tags
```

同时指定提供商和任务：

```bash
./run-ai-tests.sh --provider=aliyun --task=plan
```

### 2. 任务测试

测试特定任务下的模型表现：

```bash
node backend/scripts/test-ai-tasks.js --task=tags --provider=bytedance
```

支持的任务类型：
- `tags`: 标签生成
- `plan`: 学习计划生成
- `creative`: 创意写作
- `qa`: 问答能力

### 3. 连接测试

测试特定提供商的连接：

```bash
# 测试字节大模型
node backend/scripts/test-bytedance-connection.js

# 测试阿里云百炼
node backend/scripts/test-aliyun-connection.js

# 测试腾讯混元
node backend/scripts/test-hunyuan-connection.js
```

## 测试结果

所有测试结果将保存在 `ai-test-results` 目录中，包括：

1. JSON格式的原始测试数据
2. HTML格式的测试报告
3. Markdown格式的评估报告

## 评估指标

测试脚本会评估以下指标：

1. **响应时间**：模型生成响应所需的时间
2. **成功率**：测试任务成功完成的比例
3. **Token效率**：生成相同内容所需的token数量
4. **内容质量**：生成内容的质量（基于任务成功率）

## 手动评估

除了自动评估外，建议进行手动评估，特别是对内容质量的评估。可以考虑以下方面：

1. **内容相关性**：生成的内容是否与提示相关
2. **内容准确性**：生成的内容是否准确
3. **内容创意性**：生成的内容是否有创意
4. **内容连贯性**：生成的内容是否连贯
5. **内容完整性**：生成的内容是否完整

## 切换模型

测试完成后，可以根据评估结果选择最适合的模型。修改 `.env` 文件中的 `AI_PROVIDER` 和相应的模型ID：

```
# AI提供商配置
# 可选值: bytedance, aliyun, hunyuan
AI_PROVIDER=bytedance

# 字节大模型ID
ARK_API_MODEL=deepseek-r1-250120

# 阿里云百炼模型ID
DASHSCOPE_API_MODEL=qwen-plus-latest

# 腾讯混元模型ID
HUNYUAN_API_MODEL=hunyuan-turbos-latest
```
