-- 完整迁移脚本：一次性替换旧表

-- 1. 备份旧表（以防万一需要回滚）
RENAME TABLE LearningPlan TO LearningPlan_Old;
RENAME TABLE LearningActivity TO LearningActivity_Old;
RENAME TABLE DailyRecord TO DailyRecord_Old;

-- 2. 重命名新表为标准名称
RENAME TABLE LearningPlan_New TO LearningPlan;
RENAME TABLE LearningActivity_New TO LearningActivity;
RENAME TABLE DailyRecord_New TO DailyRecord;
RENAME TABLE BContent_New TO BContent;

-- 3. 更新索引名称
-- 为LearningPlan表添加必要的索引
ALTER TABLE LearningPlan ADD INDEX idx_user_id (user_id);
ALTER TABLE LearningPlan ADD INDEX idx_theme_id (theme_id);
ALTER TABLE LearningPlan ADD INDEX idx_created_at (created_at);

-- 为LearningActivity表添加必要的索引
ALTER TABLE LearningActivity ADD INDEX idx_activity_type (activity_type);
ALTER TABLE LearningActivity ADD INDEX idx_created_at (created_at);

-- 4. 更新外键关系（如果需要）
-- 注意：这里假设我们使用的是InnoDB引擎并且需要外键约束
-- 如果不需要外键约束，可以跳过这一步

-- 添加LearningPlan的外键
ALTER TABLE LearningPlan 
  ADD CONSTRAINT fk_learning_plan_user 
  FOREIGN KEY (user_id) REFERENCES User(id) 
  ON DELETE CASCADE;

ALTER TABLE LearningPlan 
  ADD CONSTRAINT fk_learning_plan_theme 
  FOREIGN KEY (theme_id) REFERENCES Theme(id) 
  ON DELETE SET NULL;

-- 添加LearningActivity的外键
ALTER TABLE LearningActivity 
  ADD CONSTRAINT fk_learning_activity_user 
  FOREIGN KEY (user_id) REFERENCES User(id) 
  ON DELETE CASCADE;

-- 添加DailyRecord的外键
ALTER TABLE DailyRecord 
  ADD CONSTRAINT fk_daily_record_user 
  FOREIGN KEY (user_id) REFERENCES User(id) 
  ON DELETE CASCADE;
