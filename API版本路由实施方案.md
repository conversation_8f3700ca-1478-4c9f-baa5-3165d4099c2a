# API版本路由实施方案

## 文档信息

| 文档属性 | 值 |
|---------|-----|
| 版本 | 1.0 |
| 状态 | 草稿 |
| 创建日期 | 2025-05-11 |
| 最后更新 | 2025-05-11 |
| 作者 | AIBUBB技术团队 |

## 1. 概述

本文档详细描述了AIBUBB项目API版本路由的实施方案，包括版本路由机制设计、实现方法、配置方式和集成流程。通过实施本方案，我们将提高API版本管理的灵活性和可维护性，支持多版本API的平滑过渡，并为最终统一到V2版本奠定基础。

## 2. 当前状况

目前，AIBUBB项目使用URL路径中的版本号进行API版本管理：

```javascript
// 导入路由
const apiPrefix = `/api/${config.server.apiVersion}`;
app.use(`${apiPrefix}/auth`, require('./routes/auth.routes'));
app.use(`${apiPrefix}/users`, require('./routes/user.routes'));
// ...

// V2路由（支持软删除）
app.use('/api/v2', require('./routes/tagV2.routes'));
app.use('/api/v2', require('./routes/insightV2.routes'));
app.use('/api/v2', require('./routes/exerciseV2.routes'));
app.use('/api/v2', require('./routes/noteV2.routes'));
app.use('/api/v2/themes', require('./routes/themeV2.routes'));
app.use('/api/v2/daily-contents', require('./routes/dailyContentV2.routes'));
app.use('/api/v2/learning-plans', require('./routes/learningPlanV2.routes'));
app.use('/api/v2/cleanup', require('./routes/cleanup.routes'));
app.use('/api/v2/batch', require('./routes/batchOperation.routes'));
```

这种方式存在以下问题：

1. **路由配置分散**：V1和V2版本的路由配置分散在server.js中，不易维护
2. **版本管理不灵活**：添加新版本需要修改server.js，不够灵活
3. **缺乏统一的版本控制**：缺乏统一的版本控制机制，如版本检测、版本弃用通知等
4. **兼容层缺失**：缺乏服务器端兼容层，无法将V1版本API调用转发到V2版本API

## 3. 版本路由机制设计

### 3.1 设计目标

1. **集中管理**：集中管理所有版本的路由配置
2. **灵活配置**：支持灵活配置版本路由，如URL路径版本、请求头版本或查询参数版本
3. **版本检测**：支持版本检测，自动检测客户端使用的API版本
4. **版本弃用通知**：支持版本弃用通知，提前通知客户端
5. **兼容层支持**：支持服务器端兼容层，将V1版本API调用转发到V2版本API

### 3.2 版本标识方式

我们将支持以下三种版本标识方式：

1. **URL路径版本**：在URL路径中包含版本号，如`/api/v1/users`
2. **请求头版本**：在请求头中包含版本信息，如`Accept: application/json; version=1`
3. **查询参数版本**：在查询参数中包含版本信息，如`/api/users?version=1`

默认使用URL路径版本，但支持通过配置切换到其他版本标识方式。

### 3.3 版本路由中间件

我们将实现一个版本路由中间件，用于根据版本信息路由到对应的控制器：

```javascript
/**
 * 版本路由中间件
 * @param {Object} options - 配置选项
 * @returns {Function} Express中间件
 */
const versionRouter = (options = {}) => {
  const defaultOptions = {
    defaultVersion: 'v1',
    versions: ['v1', 'v2'],
    versionExtractor: 'url', // 'url', 'header', 'query'
    headerName: 'accept-version',
    queryParam: 'version',
    deprecatedVersions: []
  };

  const config = { ...defaultOptions, ...options };

  return (req, res, next) => {
    let version = config.defaultVersion;

    // 从URL路径中提取版本
    if (config.versionExtractor === 'url') {
      const match = req.path.match(/^\/api\/v(\d+)/);
      if (match && match[1]) {
        version = `v${match[1]}`;
      }
    }
    // 从请求头中提取版本
    else if (config.versionExtractor === 'header') {
      const headerVersion = req.get(config.headerName);
      if (headerVersion) {
        version = headerVersion;
      }
    }
    // 从查询参数中提取版本
    else if (config.versionExtractor === 'query') {
      if (req.query[config.queryParam]) {
        version = `v${req.query[config.queryParam]}`;
      }
    }

    // 检查版本是否支持
    if (!config.versions.includes(version)) {
      version = config.defaultVersion;
    }

    // 检查版本是否已弃用
    if (config.deprecatedVersions.includes(version)) {
      res.set('X-API-Deprecated', 'true');
      res.set('X-API-Deprecated-Message', `版本${version}已弃用，请升级到最新版本`);
    }

    // 将版本信息添加到请求对象
    req.apiVersion = version;

    next();
  };
};
```

### 3.4 版本路由配置

我们将创建一个集中的版本路由配置文件，用于管理所有版本的路由：

```javascript
// config/version-routes.js
module.exports = {
  // 版本配置
  config: {
    defaultVersion: 'v1',
    versions: ['v1', 'v2'],
    deprecatedVersions: ['v1'],
    versionExtractor: 'url'
  },
  
  // 路由配置
  routes: [
    {
      path: '/auth',
      versions: {
        v1: require('../routes/auth.routes'),
        v2: require('../routes/auth.routes') // 暂时使用V1版本
      }
    },
    {
      path: '/users',
      versions: {
        v1: require('../routes/user.routes'),
        v2: require('../routes/user.routes') // 暂时使用V1版本
      }
    },
    {
      path: '/tags',
      versions: {
        v1: require('../routes/tag.routes'),
        v2: require('../routes/tagV2.routes')
      }
    },
    // 其他路由...
  ]
};
```

## 4. 实现方法

### 4.1 版本路由中间件实现

创建`middlewares/version-router.middleware.js`文件，实现版本路由中间件：

```javascript
/**
 * 版本路由中间件
 * 根据版本信息路由到对应的控制器
 */
const logger = require('../config/logger');

/**
 * 版本路由中间件
 * @param {Object} options - 配置选项
 * @returns {Function} Express中间件
 */
const versionRouter = (options = {}) => {
  const defaultOptions = {
    defaultVersion: 'v1',
    versions: ['v1', 'v2'],
    versionExtractor: 'url', // 'url', 'header', 'query'
    headerName: 'accept-version',
    queryParam: 'version',
    deprecatedVersions: []
  };

  const config = { ...defaultOptions, ...options };

  return (req, res, next) => {
    let version = config.defaultVersion;

    // 从URL路径中提取版本
    if (config.versionExtractor === 'url') {
      const match = req.path.match(/^\/api\/v(\d+)/);
      if (match && match[1]) {
        version = `v${match[1]}`;
      }
    }
    // 从请求头中提取版本
    else if (config.versionExtractor === 'header') {
      const headerVersion = req.get(config.headerName);
      if (headerVersion) {
        version = headerVersion;
      }
    }
    // 从查询参数中提取版本
    else if (config.versionExtractor === 'query') {
      if (req.query[config.queryParam]) {
        version = `v${req.query[config.queryParam]}`;
      }
    }

    // 检查版本是否支持
    if (!config.versions.includes(version)) {
      version = config.defaultVersion;
    }

    // 检查版本是否已弃用
    if (config.deprecatedVersions.includes(version)) {
      res.set('X-API-Deprecated', 'true');
      res.set('X-API-Deprecated-Message', `版本${version}已弃用，请升级到最新版本`);
      logger.warn(`客户端使用已弃用的API版本: ${version}, IP: ${req.ip}`);
    }

    // 将版本信息添加到请求对象
    req.apiVersion = version;

    next();
  };
};

module.exports = versionRouter;
```

### 4.2 版本路由配置实现

创建`config/version-routes.js`文件，实现版本路由配置：

```javascript
/**
 * 版本路由配置
 * 集中管理所有版本的路由
 */
module.exports = {
  // 版本配置
  config: {
    defaultVersion: 'v1',
    versions: ['v1', 'v2'],
    deprecatedVersions: ['v1'],
    versionExtractor: 'url',
    headerName: 'accept-version',
    queryParam: 'version'
  },
  
  // 路由配置
  routes: [
    {
      path: '/auth',
      versions: {
        v1: require('../routes/auth.routes'),
        v2: require('../routes/auth.routes') // 暂时使用V1版本
      }
    },
    {
      path: '/users',
      versions: {
        v1: require('../routes/user.routes'),
        v2: require('../routes/user.routes') // 暂时使用V1版本
      }
    },
    {
      path: '/tags',
      versions: {
        v1: require('../routes/tag.routes'),
        v2: require('../routes/tagV2.routes')
      }
    },
    {
      path: '/insights',
      versions: {
        v1: require('../routes/insight.routes'),
        v2: require('../routes/insightV2.routes')
      }
    },
    {
      path: '/exercises',
      versions: {
        v1: require('../routes/exercise.routes'),
        v2: require('../routes/exerciseV2.routes')
      }
    },
    {
      path: '/notes',
      versions: {
        v1: require('../routes/note.routes'),
        v2: require('../routes/noteV2.routes')
      }
    },
    {
      path: '/themes',
      versions: {
        v1: require('../routes/theme.routes'),
        v2: require('../routes/themeV2.routes')
      }
    },
    {
      path: '/learning-plans',
      versions: {
        v1: require('../routes/learningPlan.routes'),
        v2: require('../routes/learningPlanV2.routes')
      }
    },
    {
      path: '/daily-contents',
      versions: {
        v1: null, // V1版本不支持
        v2: require('../routes/dailyContentV2.routes')
      }
    },
    {
      path: '/cleanup',
      versions: {
        v1: null, // V1版本不支持
        v2: require('../routes/cleanup.routes')
      }
    },
    {
      path: '/batch',
      versions: {
        v1: null, // V1版本不支持
        v2: require('../routes/batchOperation.routes')
      }
    },
    {
      path: '/statistics',
      versions: {
        v1: require('../routes/statistics.routes'),
        v2: require('../routes/statisticsV2.routes')
      }
    }
  ]
};
```

### 4.3 版本路由注册实现

创建`utils/register-version-routes.js`文件，实现版本路由注册：

```javascript
/**
 * 版本路由注册工具
 * 用于注册版本路由配置中的路由
 */
const express = require('express');
const logger = require('../config/logger');
const versionRouter = require('../middlewares/version-router.middleware');

/**
 * 注册版本路由
 * @param {Object} app - Express应用
 * @param {Object} versionRoutes - 版本路由配置
 */
const registerVersionRoutes = (app, versionRoutes) => {
  const { config, routes } = versionRoutes;
  
  // 注册版本路由中间件
  app.use('/api', versionRouter(config));
  
  // 注册路由
  routes.forEach(route => {
    const { path, versions } = route;
    
    // 为每个版本注册路由
    Object.entries(versions).forEach(([version, router]) => {
      if (router) {
        const versionPath = `/api/${version}${path}`;
        app.use(versionPath, (req, res, next) => {
          // 检查请求的版本是否与路由版本匹配
          if (req.apiVersion === version) {
            return next();
          }
          
          // 如果版本不匹配，跳过当前路由
          return next('route');
        }, router);
        
        logger.info(`已注册路由: ${versionPath}`);
      }
    });
  });
};

module.exports = registerVersionRoutes;
```

## 5. 集成流程

### 5.1 修改server.js

修改`server.js`文件，使用版本路由注册工具注册路由：

```javascript
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const config = require('./config/config');
const logger = require('./config/logger');
const { validateEnv, checkCompatibility } = require('./config/env-validator');
const { testConnection } = require('./config/database');
const { connectRedis } = require('./config/redis');
const { apiLimiter } = require('./middlewares/rateLimit.middleware');
const { notFoundHandler, errorHandler } = require('./middlewares/error.middleware');
const { swaggerSpec, swaggerUi, swaggerUiOptions } = require('./config/swagger');
const redoc = require('redoc-express');
const versionRoutes = require('./config/version-routes');
const registerVersionRoutes = require('./utils/register-version-routes');

// 创建Express应用
const app = express();

// 基础中间件
app.use(helmet()); // 安全头
app.use(cors({
  origin: config.server.corsOrigin,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json({ limit: '1mb' }));
app.use(express.urlencoded({ extended: true, limit: '1mb' }));
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));

// 全局请求频率限制
app.use(apiLimiter);

// API文档 - Swagger UI
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, swaggerUiOptions));

// API文档 - ReDoc
app.get('/redoc', redoc({
  title: 'AIBUBB API 文档',
  specUrl: '/swagger.json',
  redocOptions: {
    hideDownloadButton: false,
    disableSearch: false,
    requiredPropsFirst: true,
    sortPropsAlphabetically: true,
  }
}));

// 提供swagger.json
app.get('/swagger.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerSpec);
});

// 测试路由
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to AIBUBB API',
    documentation: '/api-docs'
  });
});

// 注册版本路由
registerVersionRoutes(app, versionRoutes);

// 模拟数据路由（仅在开发环境可用）
if (config.server.env === 'development') {
  app.use(`/api/${config.server.apiVersion}/mock-data`, require('./routes/mockData.routes'));
}

// 404错误处理
app.use(notFoundHandler);

// 全局错误处理中间件
app.use(errorHandler);

// 启动服务器
const PORT = config.server.port; // 使用配置中的端口
app.listen(PORT, () => {
  logger.info(`服务器已启动，监听端口 ${PORT}`);
});
```

## 6. 兼容层实现

### 6.1 兼容层中间件

创建`middlewares/compatibility-layer.middleware.js`文件，实现兼容层中间件：

```javascript
/**
 * 兼容层中间件
 * 将V1版本API调用转发到V2版本API
 */
const logger = require('../config/logger');

/**
 * 兼容层中间件
 * @param {Object} options - 配置选项
 * @returns {Function} Express中间件
 */
const compatibilityLayer = (options = {}) => {
  const defaultOptions = {
    sourceVersion: 'v1',
    targetVersion: 'v2',
    enabled: true,
    transformRequest: true,
    transformResponse: true
  };

  const config = { ...defaultOptions, ...options };

  return (req, res, next) => {
    // 如果兼容层未启用或版本不是源版本，则跳过
    if (!config.enabled || req.apiVersion !== config.sourceVersion) {
      return next();
    }

    // 保存原始请求信息
    const originalReq = {
      path: req.path,
      apiVersion: req.apiVersion
    };

    // 修改请求版本
    req.apiVersion = config.targetVersion;

    // 修改请求路径
    req.url = req.url.replace(`/api/${config.sourceVersion}`, `/api/${config.targetVersion}`);

    // 转换请求
    if (config.transformRequest) {
      // 根据需要转换请求参数、请求体等
      // 这里可以添加特定的转换逻辑
    }

    // 保存原始的res.json方法
    const originalJson = res.json;

    // 重写res.json方法，用于转换响应
    res.json = function(data) {
      // 转换响应
      if (config.transformResponse) {
        // 根据需要转换响应数据
        // 这里可以添加特定的转换逻辑
      }

      // 恢复原始的res.json方法
      res.json = originalJson;

      // 调用原始的res.json方法
      return res.json(data);
    };

    logger.info(`兼容层: 将 ${originalReq.path} 从 ${originalReq.apiVersion} 转发到 ${req.apiVersion}`);

    next();
  };
};

module.exports = compatibilityLayer;
```

### 6.2 集成兼容层

修改`utils/register-version-routes.js`文件，集成兼容层：

```javascript
const express = require('express');
const logger = require('../config/logger');
const versionRouter = require('../middlewares/version-router.middleware');
const compatibilityLayer = require('../middlewares/compatibility-layer.middleware');

const registerVersionRoutes = (app, versionRoutes) => {
  const { config, routes } = versionRoutes;
  
  // 注册版本路由中间件
  app.use('/api', versionRouter(config));
  
  // 注册兼容层中间件
  app.use('/api', compatibilityLayer({
    sourceVersion: 'v1',
    targetVersion: 'v2',
    enabled: config.enableCompatibilityLayer || false
  }));
  
  // 注册路由
  routes.forEach(route => {
    const { path, versions } = route;
    
    // 为每个版本注册路由
    Object.entries(versions).forEach(([version, router]) => {
      if (router) {
        const versionPath = `/api/${version}${path}`;
        app.use(versionPath, (req, res, next) => {
          // 检查请求的版本是否与路由版本匹配
          if (req.apiVersion === version) {
            return next();
          }
          
          // 如果版本不匹配，跳过当前路由
          return next('route');
        }, router);
        
        logger.info(`已注册路由: ${versionPath}`);
      }
    });
  });
};

module.exports = registerVersionRoutes;
```

## 7. 测试验证

### 7.1 单元测试

创建`tests/unit/middlewares/version-router.middleware.test.js`文件，测试版本路由中间件：

```javascript
const { expect } = require('chai');
const sinon = require('sinon');
const versionRouter = require('../../../middlewares/version-router.middleware');

describe('版本路由中间件', () => {
  let req, res, next;

  beforeEach(() => {
    req = {
      path: '/api/v1/users',
      get: sinon.stub(),
      query: {}
    };
    res = {
      set: sinon.spy()
    };
    next = sinon.spy();
  });

  it('应该从URL路径中提取版本', () => {
    const middleware = versionRouter({
      versionExtractor: 'url'
    });

    middleware(req, res, next);

    expect(req.apiVersion).to.equal('v1');
    expect(next.calledOnce).to.be.true;
  });

  it('应该从请求头中提取版本', () => {
    req.get.withArgs('accept-version').returns('v2');

    const middleware = versionRouter({
      versionExtractor: 'header',
      headerName: 'accept-version'
    });

    middleware(req, res, next);

    expect(req.apiVersion).to.equal('v2');
    expect(next.calledOnce).to.be.true;
  });

  it('应该从查询参数中提取版本', () => {
    req.query.version = '2';

    const middleware = versionRouter({
      versionExtractor: 'query',
      queryParam: 'version'
    });

    middleware(req, res, next);

    expect(req.apiVersion).to.equal('v2');
    expect(next.calledOnce).to.be.true;
  });

  it('应该使用默认版本，如果提取的版本不受支持', () => {
    req.path = '/api/v3/users';

    const middleware = versionRouter({
      versionExtractor: 'url',
      defaultVersion: 'v1',
      versions: ['v1', 'v2']
    });

    middleware(req, res, next);

    expect(req.apiVersion).to.equal('v1');
    expect(next.calledOnce).to.be.true;
  });

  it('应该添加弃用头，如果版本已弃用', () => {
    const middleware = versionRouter({
      versionExtractor: 'url',
      deprecatedVersions: ['v1']
    });

    middleware(req, res, next);

    expect(res.set.calledWith('X-API-Deprecated', 'true')).to.be.true;
    expect(res.set.calledWith('X-API-Deprecated-Message', '版本v1已弃用，请升级到最新版本')).to.be.true;
    expect(next.calledOnce).to.be.true;
  });
});
```

### 7.2 集成测试

创建`tests/integration/version-router.test.js`文件，测试版本路由集成：

```javascript
const request = require('supertest');
const { expect } = require('chai');
const express = require('express');
const versionRouter = require('../../middlewares/version-router.middleware');

describe('版本路由集成测试', () => {
  let app;

  beforeEach(() => {
    app = express();

    // 注册版本路由中间件
    app.use(versionRouter({
      defaultVersion: 'v1',
      versions: ['v1', 'v2'],
      deprecatedVersions: ['v1']
    }));

    // 注册测试路由
    app.get('/api/v1/test', (req, res) => {
      res.json({ version: req.apiVersion, message: 'v1' });
    });

    app.get('/api/v2/test', (req, res) => {
      res.json({ version: req.apiVersion, message: 'v2' });
    });
  });

  it('应该路由到V1版本API', async () => {
    const res = await request(app).get('/api/v1/test');

    expect(res.status).to.equal(200);
    expect(res.body).to.deep.equal({ version: 'v1', message: 'v1' });
    expect(res.headers['x-api-deprecated']).to.equal('true');
  });

  it('应该路由到V2版本API', async () => {
    const res = await request(app).get('/api/v2/test');

    expect(res.status).to.equal(200);
    expect(res.body).to.deep.equal({ version: 'v2', message: 'v2' });
    expect(res.headers['x-api-deprecated']).to.be.undefined;
  });
});
```

## 8. 结论

通过实施API版本路由机制，AIBUBB项目将提高API版本管理的灵活性和可维护性，支持多版本API的平滑过渡，并为最终统一到V2版本奠定基础。本方案涵盖了版本路由机制设计、实现方法、配置方式和集成流程，提供了详细的实施步骤和测试验证方法。
