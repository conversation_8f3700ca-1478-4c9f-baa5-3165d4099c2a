#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 设置备份目录
BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="${BACKUP_DIR}/aibubb_backup_${TIMESTAMP}.sql"

echo -e "${YELLOW}开始备份AIBUBB数据库...${NC}"

# 创建备份目录（如果不存在）
mkdir -p $BACKUP_DIR

# 检查MySQL容器是否运行
if ! docker ps | grep -q aibubb-mysql; then
    echo -e "${RED}错误: MySQL容器未运行${NC}"
    exit 1
fi

# 执行数据库备份
echo -e "${YELLOW}正在备份MySQL数据...${NC}"
docker exec aibubb-mysql sh -c 'exec mysqldump -u aibubb_user -paibubb_password --databases aibubb_db' > $BACKUP_FILE

# 检查备份是否成功
if [ $? -eq 0 ]; then
    echo -e "${GREEN}数据库备份成功: $BACKUP_FILE${NC}"
    # 压缩备份文件
    gzip $BACKUP_FILE
    echo -e "${GREEN}备份文件已压缩: ${BACKUP_FILE}.gz${NC}"
else
    echo -e "${RED}数据库备份失败${NC}"
    exit 1
fi
