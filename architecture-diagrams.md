# 泡泡组件和星星组件模块化重构 - 架构图表

## 组件架构图

```
+-------------------+
|                   |
|    canvas-base    |  <-- 基础画布组件
|                   |
+-------------------+
         ^
         |
         | 继承
         |
+--------+---------+
|                  |
| +-------------+ +-------------+ 
| |             | |             |
| | bubble-canvas | | star-canvas  |  <-- 具体实现组件
| |             | |             |
| +-------------+ +-------------+
|                  |
+------------------+
```

## 模块依赖关系图

```
+-------------------+     +-------------------+
|                   |     |                   |
|   theme-manager   | <-- |  canvas-manager   |  <-- 工具模块
|                   |     |                   |
+-------------------+     +-------------------+
         ^                        ^
         |                        |
         |                        |
         |                        |
         |                        |
+--------+-----------------------+|
|                                ||
|            index.js            ||  <-- 页面逻辑
|                                ||
+--------------------------------+|
         ^                        |
         |                        |
         |                        |
+--------+-----------------------+|
|                                ||
| +-------------+ +-------------+||
| |             | |             |||
| | bubble-canvas | | star-canvas  |||  <-- 组件实现
| |             | |             |||
| +-------------+ +-------------+||
|                                ||
+--------------------------------+|
         ^                        |
         |                        |
         |                        |
+--------+------------------------+
|                                 |
|           canvas-base           |  <-- 基础组件
|                                 |
+---------------------------------+
```

## 数据流向图

```
+-------------------+
|                   |
|      API 服务      |  <-- 远程数据源
|                   |
+-------------------+
         |
         | HTTP 请求/响应
         v
+-------------------+
|                   |
|   theme-manager   |  <-- 数据管理
|                   |
+-------------------+
         |
         | 主题数据
         v
+-------------------+
|                   |
|  canvas-manager   |  <-- 组件管理
|                   |
+-------------------+
         |
         | 主题数据
         v
+-------------------+
|                   |
| bubble/star-canvas |  <-- 渲染组件
|                   |
+-------------------+
         |
         | 渲染结果
         v
+-------------------+
|                   |
|     用户界面       |  <-- 视图层
|                   |
+-------------------+
         ^
         | 用户交互
         |
+-------------------+
|                   |
|      用户         |  <-- 用户
|                   |
+-------------------+
```

## 生命周期流程图

```
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
|   页面加载(onLoad)  | --> | 创建主题管理器实例  | --> | 创建画布管理器实例  |
|                   |     |                   |     |                   |
+-------------------+     +-------------------+     +-------------------+
                                                             |
                                                             v
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
|   页面显示(onShow)  | --> |  恢复画布动画     | --> | 检查主题数据更新   |
|                   |     |                   |     |                   |
+-------------------+     +-------------------+     +-------------------+
                                                             |
                                                             v
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
|   页面隐藏(onHide)  | --> |  暂停画布动画     | --> |  保存状态(可选)   |
|                   |     |                   |     |                   |
+-------------------+     +-------------------+     +-------------------+
                                                             |
                                                             v
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
| 页面卸载(onUnload) | --> |  清理画布资源     | --> |  清理其他资源     |
|                   |     |                   |     |                   |
+-------------------+     +-------------------+     +-------------------+
```

## 交互处理流程图

```
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
|   触摸开始事件    | --> | 画布管理器处理    | --> |  具体画布组件处理  |
|                   |     |                   |     |                   |
+-------------------+     +-------------------+     +-------------------+
                                                             |
                                                             v
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
|   返回交互结果    | <-- |  判断交互类型     | <-- |  更新元素状态     |
|                   |     |                   |     |                   |
+-------------------+     +-------------------+     +-------------------+
         |
         v
+-------------------+     +-------------------+
|                   |     |                   |
|   更新页面状态    | --> |  显示主题弹窗     |
|                   |     |                   |
+-------------------+     +-------------------+
```

## 组件初始化流程图

```
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
|   调用init方法    | --> |  获取Canvas节点   | --> |  创建2D上下文     |
|                   |     |                   |     |                   |
+-------------------+     +-------------------+     +-------------------+
                                                             |
                                                             v
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
|   设置画布尺寸    | --> |  获取主题数据     | --> |  创建元素         |
|                   |     |                   |     |                   |
+-------------------+     +-------------------+     +-------------------+
                                                             |
                                                             v
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
|   启动动画循环    | --> |  初始化事件处理   | --> |  返回初始化结果   |
|                   |     |                   |     |                   |
+-------------------+     +-------------------+     +-------------------+
```

## 动画循环流程图

```
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
| requestAnimationFrame | --> | 计算时间增量    | --> |  更新元素位置     |
|                   |     |                   |     |                   |
+-------------------+     +-------------------+     +-------------------+
         ^                                                   |
         |                                                   v
         |                        +-------------------+     +-------------------+
         |                        |                   |     |                   |
         +----------------------- |  请求下一帧       | <-- |  绘制元素         |
                                  |                   |     |                   |
                                  +-------------------+     +-------------------+
```
