const User = require('./user.model');
const Theme = require('./theme.model');
const LearningPlan = require('./learningPlan.model');
const Tag = require('./tag.model');
const Exercise = require('./exercise.model');
const Insight = require('./insight.model');
const Note = require('./note.model');
const NoteLike = require('./noteLike.model');
const TagLike = require('./tagLike.model');
const NoteComment = require('./noteComment.model');
const UserFollow = require('./userFollow.model');
const TagCategory = require('./tagCategory.model');
const TagSynonym = require('./tagSynonym.model');
const TagFeedback = require('./tagFeedback.model');
const BubbleInteraction = require('./bubbleInteraction.model');
const BubbleContent = require('./bubbleContent.model');
const LearningActivity = require('./learningActivity.model');
const DailyRecord = require('./dailyRecord.model');
const DailyContent = require('./dailyContent.model');
const { sequelize } = require('../config/database');

// 定义模型之间的关联关系

// User - LearningPlan: 一对多
User.hasMany(LearningPlan, {
  foreignKey: 'user_id',
  as: 'learningPlans'
});
LearningPlan.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// Theme - LearningPlan: 一对多
Theme.hasMany(LearningPlan, {
  foreignKey: 'theme_id',
  as: 'learningPlans'
});
LearningPlan.belongsTo(Theme, {
  foreignKey: 'theme_id',
  as: 'theme'
});

// LearningPlan - Tag: 一对多
LearningPlan.hasMany(Tag, {
  foreignKey: 'plan_id',
  as: 'tags'
});
Tag.belongsTo(LearningPlan, {
  foreignKey: 'plan_id',
  as: 'learningPlan'
});

// Tag - Exercise: 一对多
Tag.hasMany(Exercise, {
  foreignKey: 'tag_id',
  as: 'exercises'
});
Exercise.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag'
});

// Tag - Insight: 一对多
Tag.hasMany(Insight, {
  foreignKey: 'tag_id',
  as: 'insights'
});
Insight.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag'
});

// Tag - Note: 一对多
Tag.hasMany(Note, {
  foreignKey: 'tag_id',
  as: 'notes'
});
Note.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag'
});

// User - Note: 一对多
User.hasMany(Note, {
  foreignKey: 'user_id',
  as: 'notes'
});
Note.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// User - NoteLike: 一对多
User.hasMany(NoteLike, {
  foreignKey: 'user_id',
  as: 'noteLikes'
});
NoteLike.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// Note - NoteLike: 一对多
Note.hasMany(NoteLike, {
  foreignKey: 'note_id',
  as: 'noteLikes'
});
NoteLike.belongsTo(Note, {
  foreignKey: 'note_id',
  as: 'note'
});

// User - TagLike: 一对多
User.hasMany(TagLike, {
  foreignKey: 'user_id',
  as: 'tagLikes'
});
TagLike.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// Tag - TagLike: 一对多
Tag.hasMany(TagLike, {
  foreignKey: 'tag_id',
  as: 'tagLikes'
});
TagLike.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag'
});

// User - NoteComment: 一对多
User.hasMany(NoteComment, {
  foreignKey: 'user_id',
  as: 'comments'
});
NoteComment.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// Note - NoteComment: 一对多
Note.hasMany(NoteComment, {
  foreignKey: 'note_id',
  as: 'noteComments'
});
NoteComment.belongsTo(Note, {
  foreignKey: 'note_id',
  as: 'note'
});

// User - UserFollow (作为关注者)
User.hasMany(UserFollow, {
  foreignKey: 'follower_id',
  as: 'following'
});
UserFollow.belongsTo(User, {
  foreignKey: 'follower_id',
  as: 'follower'
});

// User - UserFollow (作为被关注者)
User.hasMany(UserFollow, {
  foreignKey: 'following_id',
  as: 'followers'
});
UserFollow.belongsTo(User, {
  foreignKey: 'following_id',
  as: 'followingUser'
});

// Theme - TagCategory: 一对多
Theme.hasMany(TagCategory, {
  foreignKey: 'theme_id',
  as: 'categories'
});
TagCategory.belongsTo(Theme, {
  foreignKey: 'theme_id',
  as: 'theme'
});

// TagCategory - TagCategory (自关联): 一对多
TagCategory.hasMany(TagCategory, {
  foreignKey: 'parent_id',
  as: 'children'
});
TagCategory.belongsTo(TagCategory, {
  foreignKey: 'parent_id',
  as: 'parent'
});

// TagCategory - Tag: 一对多
TagCategory.hasMany(Tag, {
  foreignKey: 'category_id',
  as: 'tags'
});
Tag.belongsTo(TagCategory, {
  foreignKey: 'category_id',
  as: 'category'
});

// Tag - TagSynonym: 一对多
Tag.hasMany(TagSynonym, {
  foreignKey: 'primary_tag_id',
  as: 'synonyms'
});
TagSynonym.belongsTo(Tag, {
  foreignKey: 'primary_tag_id',
  as: 'primaryTag'
});

// Tag - TagFeedback: 一对多
Tag.hasMany(TagFeedback, {
  foreignKey: 'tag_id',
  as: 'feedbacks'
});
TagFeedback.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag'
});

// User - TagFeedback: 一对多
User.hasMany(TagFeedback, {
  foreignKey: 'user_id',
  as: 'tagFeedbacks'
});
TagFeedback.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// User - BubbleInteraction: 一对多
User.hasMany(BubbleInteraction, {
  foreignKey: 'user_id',
  as: 'bubbleInteractions'
});
BubbleInteraction.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// Tag - BubbleInteraction: 一对多
Tag.hasMany(BubbleInteraction, {
  foreignKey: 'tag_id',
  as: 'bubbleInteractions'
});
BubbleInteraction.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag'
});

// Tag - BubbleContent: 一对多
Tag.hasMany(BubbleContent, {
  foreignKey: 'tag_id',
  as: 'bubbleContents'
});
BubbleContent.belongsTo(Tag, {
  foreignKey: 'tag_id',
  as: 'tag'
});

// User - LearningActivity: 一对多
User.hasMany(LearningActivity, {
  foreignKey: 'user_id',
  as: 'learningActivities'
});
LearningActivity.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// LearningPlan - LearningActivity: 一对多
LearningPlan.hasMany(LearningActivity, {
  foreignKey: 'plan_id',
  as: 'learningActivities'
});
LearningActivity.belongsTo(LearningPlan, {
  foreignKey: 'plan_id',
  as: 'learningPlan'
});

// User - DailyRecord: 一对多
User.hasMany(DailyRecord, {
  foreignKey: 'user_id',
  as: 'dailyRecords'
});
DailyRecord.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

module.exports = {
  sequelize,
  User,
  Theme,
  LearningPlan,
  Tag,
  Exercise,
  Insight,
  Note,
  NoteLike,
  TagLike,
  NoteComment,
  UserFollow,
  TagCategory,
  TagSynonym,
  TagFeedback,
  BubbleInteraction,
  BubbleContent,
  LearningActivity,
  DailyRecord,
  DailyContent
};
